!@!@! 2025.08.05T10:36:11.749+0800 === java.lang.NullPointerException: null
	at com.djcps.log.operation.LogAspect.getRequest(LogAspect.java:118) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:66) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$cb687b90.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$67b622fc.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]

!@!@! 2025.08.05T10:36:13.429+0800 === java.lang.NullPointerException: null
	at com.djcps.log.operation.LogAspect.getRequest(LogAspect.java:118) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at com.djcps.log.operation.LogAspect.getParameter(LogAspect.java:128) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:85) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$cb687b90.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$67b622fc.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]

!@!@! 2025.08.05T10:36:13.437+0800 === {"userid":"-","operatingTime":"2025-08-05T10:36:13.431+0800","signature":"String com.djcps.djeasyorder.feign.product.ProductFeign.getLayerList()","excuteTime":1687}
!@!@! 2025.08.05T11:31:30.766+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-04","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"null08d017d7-a067-4023-ba9f-17322e94b696","operatingTime":"2025-08-05T11:31:30.746+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":473}
!@!@! 2025.08.05T11:31:30.973+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"0caac329d533400a80315663f9abd12e","bizType":"deliver","cycleType":"day","dataDate":"2025-08-04","execEndTime":"2025-08-05T11:31:30.792","execStartTime":"2025-08-05T11:31:30.113","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":33,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"null4d55da97-a7f6-47b6-b1fe-785baa7d0637","operatingTime":"2025-08-05T11:31:30.968+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":159}
!@!@! 2025.08.05T11:31:33.803+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-03","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"null1d7e0818-92ba-45f9-b4e9-9151c2e74ba4","operatingTime":"2025-08-05T11:31:33.802+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":219}
!@!@! 2025.08.05T11:31:33.907+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"0caac329d533400a80315663f9abd12e","bizType":"deliver","cycleType":"day","dataDate":"2025-08-03","execEndTime":"2025-08-05T11:31:33.806","execStartTime":"2025-08-05T11:31:30.113","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":34,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"null83056913-cae6-4c1e-8a25-fd806720a28d","operatingTime":"2025-08-05T11:31:33.907+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":96}
!@!@! 2025.08.05T11:31:36.767+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-02","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"null8357ad42-ff5d-41fa-b44a-152e369539f3","operatingTime":"2025-08-05T11:31:36.765+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":495}
!@!@! 2025.08.05T11:31:36.919+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"0caac329d533400a80315663f9abd12e","bizType":"deliver","cycleType":"day","dataDate":"2025-08-02","execEndTime":"2025-08-05T11:31:36.770","execStartTime":"2025-08-05T11:31:30.113","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":35,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"nullb530cf75-ba23-4d92-bc73-5daf09dc1e2d","operatingTime":"2025-08-05T11:31:36.918+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":143}
!@!@! 2025.08.05T11:36:25.087+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-04","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"nullccef03a1-45d9-4482-87f8-7c97fd76f541","operatingTime":"2025-08-05T11:36:25.084+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":543}
!@!@! 2025.08.05T11:36:25.345+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","bizType":"deliver","cycleType":"day","dataDate":"2025-08-04","execEndTime":"2025-08-05T11:36:25.094","execStartTime":"2025-08-05T11:36:24.506","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":36,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"null40bd00f4-6108-43e0-91f9-24d314dde0c9","operatingTime":"2025-08-05T11:36:25.344+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":241}
!@!@! 2025.08.05T11:36:28.689+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","originData":"{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"childpaymenttime\":\"07月22日\"}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"cnt_no\":27,\"productarea_no\":43.78,\"order_count_no\":377}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"code\":200,\"data\":{\"areaByDelivery\":0,\"customerNumByDelivery\":0},\"msg\":\"success\",\"traceId\":\"61430ccc994f405db10584bfcbce95cc\",\"success\":true}\n---\n24H交付率 按到达\n{'current': '-', 'pre': '-', 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比\n---\n24H交付率 按出库\n{'current': '-', 'pre': '-', 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比","prompt":"查询到的数据是经过计算的,不需要你进行百分比的计算;\n查询数据并按照<content>标签中的内容格式(不包含<content>标签)进行返回,不需要任何多余的内容\n如果某一个行的数据都没有或者数据异常,则不展示该行,如果最终展示的内容无意义也不需要展示出来 :类似 \"集中交付周期数据无效\",\"当日24H交付率数据无效/数据无效\"就不用显示出来;\n集中交付周期为X小时-X小时 :\n- 这一条中的D是 start_time中的数据,仅日期即可,去掉个位数中的0\n- 这一条中的X是 start_time中的数据,仅小时即可,去掉个位数中的0\n- 这一条中的Y是 end_time中的数据,仅小时即可,去掉个位数中的0\n- 例如 start_time: 2025-07-25 07:00:00,end 2025-07-25 09:00:00, 则最终输出内容为:\" •  集中交付周期：25日7am-7am；\";\n- 例如 start_time: 2025-07-25 09:00:00,end 2025-07-25 11:00:00, 则最终输出内容为:\" •  集中交付周期：25日9am-11pm；\";\n如果所有数据都没有,则展示\"本期无总结内容\";\n只返回标签内的内容,不需要任何多余的内容返回:\n<content>\n•  截止昨日未交付订单X笔，涉及客户X家，X万㎡；\n•  最早未交付订单时间为X月X日；\n•  当日24H交付率为X%（按出库，环比X%）/X%（按送达，环比X%）；\n•  集中交付周期：D日Xam-Ypm；\n•  90%订单能保证在X小时内送出；\n•  X区域交期波动明显，需重点关注；\n</content>","resultId":36,"skey":"400","subBizType":"summary","taskId":18,"templateId":3},"reponseBody":{"code":"10001200","data":6,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveItemCommon.do","linkKey":"null956c43fe-847c-4150-8688-78f65b16ac3f","operatingTime":"2025-08-05T11:36:28.685+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveResultItem(TaskResultItemSaveBo)","excuteTime":485}
!@!@! 2025.08.05T11:36:31.958+0800 === {"ip":"-","requestParam":{},"requestBody":{"analysisResult":"•  截止昨日未交付订单377笔，涉及客户27家，43.78万㎡；\n•  最早未交付订单时间为07月22日；","taskItemId":6},"reponseBody":{"code":"10001200","data":"更新成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/updateItemCommon.do","linkKey":"null6a7d969d-9a6c-49ba-b258-19eab7c35b4e","operatingTime":"2025-08-05T11:36:31.955+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.updateResultItem(TaskResultItemUpdateBo)","excuteTime":224}
!@!@! 2025.08.05T11:36:32.478+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-03","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"null143e9825-233a-499b-9c02-0eab753766e3","operatingTime":"2025-08-05T11:36:32.476+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":512}
!@!@! 2025.08.05T11:36:32.743+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","bizType":"deliver","cycleType":"day","dataDate":"2025-08-03","execEndTime":"2025-08-05T11:36:32.483","execStartTime":"2025-08-05T11:36:24.506","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":37,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"null1c65f02d-a1ac-4e3c-9591-55c158d390bc","operatingTime":"2025-08-05T11:36:32.742+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":252}
!@!@! 2025.08.05T11:36:35.458+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","originData":"{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"childpaymenttime\":\"07月20日\"}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"cnt_no\":27,\"productarea_no\":46.45,\"order_count_no\":406}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"code\":200,\"data\":{\"areaByDelivery\":0,\"customerNumByDelivery\":0},\"msg\":\"success\",\"traceId\":\"09b129f4f7b64d88874dce9e5eeddd8f\",\"success\":true}\n---\n24H交付率 按到达\n{'current': '-', 'pre': '-', 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比\n---\n24H交付率 按出库\n{'current': '-', 'pre': '-', 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比","prompt":"查询到的数据是经过计算的,不需要你进行百分比的计算;\n查询数据并按照<content>标签中的内容格式(不包含<content>标签)进行返回,不需要任何多余的内容\n如果某一个行的数据都没有或者数据异常,则不展示该行,如果最终展示的内容无意义也不需要展示出来 :类似 \"集中交付周期数据无效\",\"当日24H交付率数据无效/数据无效\"就不用显示出来;\n集中交付周期为X小时-X小时 :\n- 这一条中的D是 start_time中的数据,仅日期即可,去掉个位数中的0\n- 这一条中的X是 start_time中的数据,仅小时即可,去掉个位数中的0\n- 这一条中的Y是 end_time中的数据,仅小时即可,去掉个位数中的0\n- 例如 start_time: 2025-07-25 07:00:00,end 2025-07-25 09:00:00, 则最终输出内容为:\" •  集中交付周期：25日7am-7am；\";\n- 例如 start_time: 2025-07-25 09:00:00,end 2025-07-25 11:00:00, 则最终输出内容为:\" •  集中交付周期：25日9am-11pm；\";\n如果所有数据都没有,则展示\"本期无总结内容\";\n只返回标签内的内容,不需要任何多余的内容返回:\n<content>\n•  截止昨日未交付订单X笔，涉及客户X家，X万㎡；\n•  最早未交付订单时间为X月X日；\n•  当日24H交付率为X%（按出库，环比X%）/X%（按送达，环比X%）；\n•  集中交付周期：D日Xam-Ypm；\n•  90%订单能保证在X小时内送出；\n•  X区域交期波动明显，需重点关注；\n</content>","resultId":37,"skey":"400","subBizType":"summary","taskId":18,"templateId":3},"reponseBody":{"code":"10001200","data":7,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveItemCommon.do","linkKey":"nulld11cc4db-71b1-4382-8816-8d2c8584e49a","operatingTime":"2025-08-05T11:36:35.456+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveResultItem(TaskResultItemSaveBo)","excuteTime":345}
!@!@! 2025.08.05T11:36:36.909+0800 === {"ip":"-","requestParam":{},"requestBody":{"analysisResult":"•  截止昨日未交付订单27笔，涉及客户406家，46.45万㎡；\n•  最早未交付订单时间为07月20日；","taskItemId":7},"reponseBody":{"code":"10001200","data":"更新成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/updateItemCommon.do","linkKey":"nulld8b191f1-e76f-4513-8147-844673b4474f","operatingTime":"2025-08-05T11:36:36.908+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.updateResultItem(TaskResultItemUpdateBo)","excuteTime":212}
!@!@! 2025.08.05T11:36:37.427+0800 === {"ip":"-","requestParam":{},"requestBody":{"cycleType":"day","dataDate":"2025-08-02","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":"删除成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/deleteCommon.do","linkKey":"null02241e0e-1933-4cb9-bdfa-14ace53c05c6","operatingTime":"2025-08-05T11:36:37.426+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.deleteByTemplateDataDate(TaskResultDeleteBo)","excuteTime":508}
!@!@! 2025.08.05T11:36:37.648+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","bizType":"deliver","cycleType":"day","dataDate":"2025-08-02","execEndTime":"2025-08-05T11:36:37.431","execStartTime":"2025-08-05T11:36:24.506","skey":"400","taskId":18},"reponseBody":{"code":"10001200","data":38,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveCommon.do","linkKey":"null49c40156-e0ab-4243-9352-ac4ff7b1f820","operatingTime":"2025-08-05T11:36:37.647+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveTaskResult(TaskResultSaveBo)","excuteTime":208}
!@!@! 2025.08.05T11:36:40.336+0800 === {"ip":"-","requestParam":{},"requestBody":{"batchNo":"7e039afc306f4b2b87b31d26b2ca3fba","originData":"{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"childpaymenttime\":\"07月20日\"}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[{\"cnt_no\":27,\"productarea_no\":46.45,\"order_count_no\":406}],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"msg\":\"请求成功！\",\"code\":\"10001200\",\"data\":[],\"success\":true}\n{\"code\":200,\"data\":{\"areaByDelivery\":0,\"customerNumByDelivery\":0},\"msg\":\"success\",\"traceId\":\"b56b771082f94dc0b20d891c6f74826f\",\"success\":true}\n---\n24H交付率 按到达\n{'current': '-', 'pre': 0.0, 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比\n---\n24H交付率 按出库\n{'current': '-', 'pre': 0.0, 'diff': '-'}\ncurrent：24小时交付率\ndiff：环比","prompt":"查询到的数据是经过计算的,不需要你进行百分比的计算;\n查询数据并按照<content>标签中的内容格式(不包含<content>标签)进行返回,不需要任何多余的内容\n如果某一个行的数据都没有或者数据异常,则不展示该行,如果最终展示的内容无意义也不需要展示出来 :类似 \"集中交付周期数据无效\",\"当日24H交付率数据无效/数据无效\"就不用显示出来;\n集中交付周期为X小时-X小时 :\n- 这一条中的D是 start_time中的数据,仅日期即可,去掉个位数中的0\n- 这一条中的X是 start_time中的数据,仅小时即可,去掉个位数中的0\n- 这一条中的Y是 end_time中的数据,仅小时即可,去掉个位数中的0\n- 例如 start_time: 2025-07-25 07:00:00,end 2025-07-25 09:00:00, 则最终输出内容为:\" •  集中交付周期：25日7am-7am；\";\n- 例如 start_time: 2025-07-25 09:00:00,end 2025-07-25 11:00:00, 则最终输出内容为:\" •  集中交付周期：25日9am-11pm；\";\n如果所有数据都没有,则展示\"本期无总结内容\";\n只返回标签内的内容,不需要任何多余的内容返回:\n<content>\n•  截止昨日未交付订单X笔，涉及客户X家，X万㎡；\n•  最早未交付订单时间为X月X日；\n•  当日24H交付率为X%（按出库，环比X%）/X%（按送达，环比X%）；\n•  集中交付周期：D日Xam-Ypm；\n•  90%订单能保证在X小时内送出；\n•  X区域交期波动明显，需重点关注；\n</content>","resultId":38,"skey":"400","subBizType":"summary","taskId":18,"templateId":3},"reponseBody":{"code":"10001200","data":8,"msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/saveItemCommon.do","linkKey":"nulle38a56f8-c7c4-4b91-81c8-966e450b040f","operatingTime":"2025-08-05T11:36:40.335+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.saveResultItem(TaskResultItemSaveBo)","excuteTime":382}
!@!@! 2025.08.05T11:36:42.152+0800 === {"ip":"-","requestParam":{},"requestBody":{"analysisResult":"•  截止昨日未交付订单406笔，涉及客户27家，46.45万㎡；\n•  最早未交付订单时间为07月20日；","taskItemId":8},"reponseBody":{"code":"10001200","data":"更新成功","msg":"请求成功！","success":true},"OS":"Java/17.0.14","requestURL":"/DJEASYORDER/result/updateItemCommon.do","linkKey":"nullc92ba5d4-af57-493a-893d-4e519027c924","operatingTime":"2025-08-05T11:36:42.152+0800","signature":"HttpResult com.djcps.djeasyorder.smartbrief.controller.TaskResultController.updateResultItem(TaskResultItemUpdateBo)","excuteTime":210}
