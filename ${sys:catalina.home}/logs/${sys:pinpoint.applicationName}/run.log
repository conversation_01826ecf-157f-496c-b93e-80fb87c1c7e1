!@!@! 2025.08.04T14:36:34.255+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:39:14.426+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:50:08.996+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:51:16.695+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:53:43.658+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:56:47.270+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:58:22.623+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T14:59:35.449+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:03:14.615+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:03:17.717+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:03:27.175+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$99ae7c42 ${ctx:PtxId}
!@!@! 2025.08.04T15:03:35.122+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.625 seconds (JVM running for 23.119) ${ctx:PtxId}
!@!@! 2025.08.04T15:03:53.621+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:03:53.623+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.104+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 接收到产品服务MQ消息:[{"id":"d4c21f91-6144-4baa-8b96-d3f40a4edf13","name":"5bc瓦131","supplierId":"400","material":"EB2EB","layer":5,"edge":"BC瓦","operatorType":"add"}] ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.111+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 开始处理产品信息:{"edge":"BC瓦","id":"d4c21f91-6144-4baa-8b96-d3f40a4edf13","layer":5,"material":"EB2EB","name":"5bc瓦131","operatorType":"add","supplierId":"400"} ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.806+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.806+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Accept: application/json;charset=utf-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.806+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:06:16.807+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> END HTTP (0-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:06:26.906+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- ERROR HttpHostConnectException: Connect to djproductserver:80 [djproductserver/*************, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) (10098ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:06:26.907+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] org.apache.http.conn.HttpHostConnectException: Connect to djproductserver:80 [djproductserver/*************, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$d6f3864.syncMqData(<generated>)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$b79177e6.productEdit(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114)
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 83 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:06:26.907+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:06:26.913+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10117 ${ctx:PtxId}
!@!@! 2025.08.04T15:06:26.918+0800 ERROR com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - MQ消费失败: feign.RetryableException: Connect to djproductserver:80 [djproductserver/*************, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) executing POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do
	at feign.FeignException.errorExecuting(FeignException.java:65) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77) ~[feign-core-9.7.0.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102) ~[feign-core-9.7.0.jar:?]
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$d6f3864.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$b79177e6.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to djproductserver:80 [djproductserver/*************, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_442]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_442]
	at java.net.Socket.connect(Socket.java:607) ~[?:1.8.0_442]
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.941+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://djorg/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.941+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.941+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.941+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.941+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.942+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:07:31.942+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:07:41.980+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- ERROR HttpHostConnectException: Connect to djorg:80 [djorg/************, djorg/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) (10038ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:07:41.982+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] org.apache.http.conn.HttpHostConnectException: Connect to djorg:80 [djorg/************, djorg/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source)
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$9452691f.getUnionForSupplierId(<generated>)
	at com.djcps.djeasyorder.common.utils.UserUtil.setUnionLocalUser(UserUtil.java:127)
	at com.djcps.djeasyorder.common.config.SysInterceptor.preHandle(SysInterceptor.java:57)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:136)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:986)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 107 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:07:41.984+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:07:41.985+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10097 ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.010+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 接收到产品服务MQ消息:[{"id":"cf268808-2671-4a8b-bafd-f20cf66af4f9","name":"5bc瓦1331","supplierId":"400","material":"EB2EB","layer":5,"edge":"BC瓦","operatorType":"edit"}] ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.014+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 开始处理产品信息:{"edge":"BC瓦","id":"cf268808-2671-4a8b-bafd-f20cf66af4f9","layer":5,"material":"EB2EB","name":"5bc瓦1331","operatorType":"edit","supplierId":"400"} ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.749+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.750+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Accept: application/json;charset=utf-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.750+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:09:10.750+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> END HTTP (0-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:09:20.781+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- ERROR HttpHostConnectException: Connect to djproductserver:80 [djproductserver/***********, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) (10030ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:09:20.783+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] org.apache.http.conn.HttpHostConnectException: Connect to djproductserver:80 [djproductserver/***********, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$d6f3864.syncMqData(<generated>)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$b79177e6.productEdit(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114)
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 83 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:09:20.784+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:09:20.786+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10038 ${ctx:PtxId}
!@!@! 2025.08.04T15:09:20.789+0800 ERROR com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - MQ消费失败: feign.RetryableException: Connect to djproductserver:80 [djproductserver/***********, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) executing POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do
	at feign.FeignException.errorExecuting(FeignException.java:65) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77) ~[feign-core-9.7.0.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102) ~[feign-core-9.7.0.jar:?]
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$d6f3864.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$b79177e6.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to djproductserver:80 [djproductserver/***********, djproductserver/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_442]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_442]
	at java.net.Socket.connect(Socket.java:607) ~[?:1.8.0_442]
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:10:37.095+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:10:40.344+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:10:49.939+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$ed4dc755 ${ctx:PtxId}
!@!@! 2025.08.04T15:10:58.369+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 23.392 seconds (JVM running for 24.953) ${ctx:PtxId}
!@!@! 2025.08.04T15:11:16.564+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:11:16.565+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://djorg/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:12:05.444+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:12:15.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- ERROR HttpHostConnectException: Connect to djorg:80 [djorg/**************, djorg/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed) (10061ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:12:15.508+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] org.apache.http.conn.HttpHostConnectException: Connect to djorg:80 [djorg/**************, djorg/2001:0:0:0:0:0:0:1] failed: No route to host (connect failed)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source)
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$d373d4b3.getUnionForSupplierId(<generated>)
	at com.djcps.djeasyorder.common.utils.UserUtil.setUnionLocalUser(UserUtil.java:127)
	at com.djcps.djeasyorder.common.config.SysInterceptor.preHandle(SysInterceptor.java:57)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:136)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:986)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: No route to host (connect failed)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 107 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:12:15.509+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:12:15.517+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10097 ${ctx:PtxId}
!@!@! 2025.08.04T15:12:55.259+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:12:58.231+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:07.706+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$ba2d5bd ${ctx:PtxId}
!@!@! 2025.08.04T15:13:16.492+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 22.142 seconds (JVM running for 23.46) ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.426+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:30880/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.426+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.427+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.427+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.427+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.427+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.427+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.477+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- ERROR HttpHostConnectException: Connect to **************:30880 [/**************] failed: Connection refused (Connection refused) (49ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.477+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] org.apache.http.conn.HttpHostConnectException: Connect to **************:30880 [/**************] failed: Connection refused (Connection refused)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source)
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$5c433120.getUnionForSupplierId(<generated>)
	at com.djcps.djeasyorder.common.utils.UserUtil.setUnionLocalUser(UserUtil.java:127)
	at com.djcps.djeasyorder.common.config.SysInterceptor.preHandle(SysInterceptor.java:57)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:136)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:986)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 107 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.477+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:13:32.481+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:93 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:36.430+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:36.431+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.201+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:30880/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.202+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.203+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.203+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.203+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.203+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.203+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.267+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- ERROR HttpHostConnectException: Connect to **************:30880 [/**************] failed: Connection refused (Connection refused) (63ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.269+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] org.apache.http.conn.HttpHostConnectException: Connect to **************:30880 [/**************] failed: Connection refused (Connection refused)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source)
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$5c433120.getUnionForSupplierId(<generated>)
	at com.djcps.djeasyorder.common.utils.UserUtil.setUnionLocalUser(UserUtil.java:127)
	at com.djcps.djeasyorder.common.config.SysInterceptor.preHandle(SysInterceptor.java:57)
	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:136)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:986)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 107 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.270+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T15:13:41.271+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:72 ${ctx:PtxId}
!@!@! 2025.08.04T15:17:28.482+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:17:31.709+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:17:41.239+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$a7ca9666 ${ctx:PtxId}
!@!@! 2025.08.04T15:17:48.890+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.326 seconds (JVM running for 22.644) ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.145+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.146+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.237+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (90ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.237+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.237+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.237+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:18:02 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.238+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.238+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.238+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.238+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.261+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:158 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.268+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.653+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:11 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:02.661+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'userId' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy444.pageQuery(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.lambda$pageQuery$0(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:121) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:105) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:26) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$26033fe9.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'userId' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:419) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:164) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:162) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextMap.get(DynamicContext.java:94) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:108) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTNotEq.getValueBody(ASTNotEq.java:50) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:05.799+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:18:05.800+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.086+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.087+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (154ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:20:12 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.242+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.243+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:159 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.245+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.700+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:2 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:12.708+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'userId' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy444.pageQuery(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.lambda$pageQuery$0(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:121) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:105) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:26) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$26033fe9.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'userId' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:419) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:164) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:162) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextMap.get(DynamicContext.java:94) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:108) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTNotEq.getValueBody(ASTNotEq.java:50) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:31.068+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:20:34.040+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:43.499+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$b89d8dc3 ${ctx:PtxId}
!@!@! 2025.08.04T15:20:53.080+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 23.004 seconds (JVM running for 24.354) ${ctx:PtxId}
!@!@! 2025.08.04T15:21:12.431+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:12.432+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.031+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.031+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.031+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.031+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.031+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.032+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.032+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.170+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (138ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.171+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.171+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.171+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:21:27 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.171+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.171+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.172+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.172+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.201+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:193 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.210+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.677+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10 ${ctx:PtxId}
!@!@! 2025.08.04T15:21:27.684+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'batchNo' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy444.pageQuery(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.lambda$pageQuery$0(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:121) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:105) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$257c6be0.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'batchNo' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:419) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:164) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:162) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextMap.get(DynamicContext.java:94) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:108) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTNotEq.getValueBody(ASTNotEq.java:50) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.976+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.977+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.977+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.978+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.978+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:22:25.978+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.062+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (83ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.062+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.062+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.062+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:22:26 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.063+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.063+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.063+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.064+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:22:26.067+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:93 ${ctx:PtxId}
!@!@! 2025.08.04T15:22:27.182+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:3017 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.287+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.288+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.289+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.289+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.289+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.289+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.289+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.435+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (144ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.435+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.435+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.436+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:25:09 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.436+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.436+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.437+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.437+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.440+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:154 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.442+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.924+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:5 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:09.930+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'batchNo' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy444.pageQuery(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.lambda$pageQuery$0(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:121) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:105) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$257c6be0.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.reflection.ReflectionException: There is no getter for property named 'batchNo' in 'class com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo'
	at org.apache.ibatis.reflection.Reflector.getGetInvoker(Reflector.java:419) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaClass.getGetInvoker(MetaClass.java:164) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.getBeanProperty(BeanWrapper.java:162) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.wrapper.BeanWrapper.get(BeanWrapper.java:49) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.reflection.MetaObject.getValue(MetaObject.java:122) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextMap.get(DynamicContext.java:94) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:108) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTNotEq.getValueBody(ASTNotEq.java:50) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTAnd.getValueBody(ASTAnd.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateBoolean(ExpressionEvaluator.java:32) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.IfSqlNode.apply(IfSqlNode.java:34) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:25:51.944+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:25:54.953+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:04.762+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$5318bb62 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:13.647+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 22.664 seconds (JVM running for 24.031) ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (106ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:26:26 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.673+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.674+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.674+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.700+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:166 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:26.709+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:26:27.374+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:220 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:29.807+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:26:29.807+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.040+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.040+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.040+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.041+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.041+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.041+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.041+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.192+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (151ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.192+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.192+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.193+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:29:52 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.193+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.193+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.193+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.193+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.207+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:168 ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.211+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:29:52.880+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:241 ${ctx:PtxId}
!@!@! 2025.08.04T15:30:23.872+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:30:26.738+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:30:36.288+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$974156dc ${ctx:PtxId}
!@!@! 2025.08.04T15:30:44.214+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.289 seconds (JVM running for 22.637) ${ctx:PtxId}
!@!@! 2025.08.04T15:31:04.218+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:31:04.219+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.151+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.152+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.341+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (188ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.342+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.342+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.342+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:36:22 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.342+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.342+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.343+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.343+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.367+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:240 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:22.376+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:36:23.467+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:203 ${ctx:PtxId}
!@!@! 2025.08.04T15:36:23.474+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
### The error may exist in file [/Users/<USER>/Documents/workspace/djeasyorder/target/classes/com/djcps/djeasyorder/smartbrief/mapper/TaskResultMapper.xml]
### The error may involve com.djcps.djeasyorder.smartbrief.dao.TaskResultDao.pageQuery_COUNT
### The error occurred while handling results
### SQL: SELECT count(0) FROM task_result WHERE skey = ? AND supplier_id = ? AND org_id = ? AND user_id = ? AND biz_type = ? AND cycle_type = ? LIMIT ?, ?
### Cause: java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy444.pageQuery(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.lambda$pageQuery$0(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:121) ~[classes/:?]
	at com.djcps.djeasyorder.common.utils.PageUtils.pageQuery(PageUtils.java:105) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:31) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$39417004.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
### The error may exist in file [/Users/<USER>/Documents/workspace/djeasyorder/target/classes/com/djcps/djeasyorder/smartbrief/mapper/TaskResultMapper.xml]
### The error may involve com.djcps.djeasyorder.smartbrief.dao.TaskResultDao.pageQuery_COUNT
### The error occurred while handling results
### SQL: SELECT count(0) FROM task_result WHERE skey = ? AND supplier_id = ? AND org_id = ? AND user_id = ? AND biz_type = ? AND cycle_type = ? LIMIT ?, ?
### Cause: java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:150) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
Caused by: java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
	at java.util.ArrayList.rangeCheck(ArrayList.java:659) ~[?:1.8.0_442]
	at java.util.ArrayList.get(ArrayList.java:435) ~[?:1.8.0_442]
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:139) ~[pagehelper-5.1.8.jar:?]
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:148) ~[pagehelper-5.1.8.jar:?]
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:97) ~[pagehelper-5.1.8.jar:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:95) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 92 more
 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:04.946+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T15:44:08.026+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:17.627+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$d8337139 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:25.327+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 22.423 seconds (JVM running for 23.813) ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.557+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (92ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:44:39 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.650+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.651+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.651+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.673+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:157 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:39.681+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:44:40.428+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:235 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:44.248+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T15:44:44.248+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.960+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T15:59:36.961+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.110+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (147ms) ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.111+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.112+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.112+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 07:59:37 GMT ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.112+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.113+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.113+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.113+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.116+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:157 ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.117+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T15:59:37.813+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:212 ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.808+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.970+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (160ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.970+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.970+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.970+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:00:49 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.970+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.971+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.971+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.971+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.973+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:166 ${ctx:PtxId}
!@!@! 2025.08.04T16:00:48.974+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:00:49.642+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:212 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.912+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.913+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.913+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.913+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.913+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.913+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.914+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.991+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (76ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.991+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.992+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.992+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:01:11 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.992+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.992+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.993+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.993+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.995+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:84 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:10.997+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:01:11.722+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:231 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.428+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.428+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.428+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.429+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.429+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.429+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.429+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.504+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (72ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.505+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.505+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.505+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:01:49 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.505+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.508+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:84 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:49.509+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:01:50.612+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:531 ${ctx:PtxId}
!@!@! 2025.08.04T16:01:50.621+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.binding.BindingException: Parameter 'ids' not found. Available parameters are [collection, list]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy445.getByTaskResultIds(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:37) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$8e2d64dc.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.binding.BindingException: Parameter 'ids' not found. Available parameters are [collection, list]
	at org.apache.ibatis.session.defaults.DefaultSqlSession$StrictMap.get(DefaultSqlSession.java:343) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:115) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateIterable(ExpressionEvaluator.java:43) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ForEachSqlNode.apply(ForEachSqlNode.java:55) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 89 more
 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:19.477+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T16:02:22.746+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:32.270+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$e8958dc4 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:39.956+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.459 seconds (JVM running for 22.771) ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.069+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.167+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (97ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.167+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.167+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.167+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:02:54 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.168+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.168+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.168+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.168+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.195+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:166 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.203+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.894+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:271 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:54.899+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.binding.BindingException: Parameter 'ids' not found. Available parameters are [collection, list]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:77) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at com.sun.proxy.$Proxy139.selectList(Unknown Source) ~[?:?]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230) ~[mybatis-spring-1.3.2.jar:1.3.2]
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy445.getByTaskResultIds(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:37) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$41a336a5.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.ibatis.binding.BindingException: Parameter 'ids' not found. Available parameters are [collection, list]
	at org.apache.ibatis.session.defaults.DefaultSqlSession$StrictMap.get(DefaultSqlSession.java:343) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicContext$ContextAccessor.getProperty(DynamicContext.java:115) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.OgnlRuntime.getProperty(OgnlRuntime.java:2685) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.ASTProperty.getValueBody(ASTProperty.java:114) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.evaluateGetValueBody(SimpleNode.java:212) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.SimpleNode.getValue(SimpleNode.java:258) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:470) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.ognl.Ognl.getValue(Ognl.java:434) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.OgnlCache.getValue(OgnlCache.java:44) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ExpressionEvaluator.evaluateIterable(ExpressionEvaluator.java:43) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.ForEachSqlNode.apply(ForEachSqlNode.java:55) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.MixedSqlNode.apply(MixedSqlNode.java:33) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.scripting.xmltags.DynamicSqlSource.getBoundSql(DynamicSqlSource.java:41) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.mapping.MappedStatement.getBoundSql(MappedStatement.java:292) ~[mybatis-3.4.6.jar:3.4.6]
	at com.djcps.djeasyorder.common.config.SkeyInterceptor.intercept(SkeyInterceptor.java:92) ~[classes/:?]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.4.6.jar:3.4.6]
	at com.sun.proxy.$Proxy482.query(Unknown Source) ~[?:?]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148) ~[mybatis-3.4.6.jar:3.4.6]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141) ~[mybatis-3.4.6.jar:3.4.6]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433) ~[mybatis-spring-1.3.2.jar:1.3.2]
	... 89 more
 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:57.708+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T16:02:57.708+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:03.677+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T16:04:06.719+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:16.106+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$844e129 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:23.798+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.057 seconds (JVM running for 22.386) ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.162+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.162+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.162+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.163+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.163+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.163+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.163+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.260+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (97ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.260+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.260+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.260+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:04:37 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.261+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.261+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.261+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.261+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.284+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:161 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:37.292+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:04:38.227+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:392 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:40.947+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T16:04:40.948+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.312+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.393+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (79ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.394+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.394+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.395+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:05:25 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.395+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.395+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.396+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.396+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.398+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:87 ${ctx:PtxId}
!@!@! 2025.08.04T16:05:25.401+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:05:44.783+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:18895 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.783+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.783+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.783+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.784+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.784+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.784+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.785+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.848+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (62ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.848+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.848+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.849+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:06:05 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.849+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.849+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.849+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.849+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.851+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:73 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:05.853+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:06:12.084+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:5568 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:17.881+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T16:06:21.041+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:30.908+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$c2059652 ${ctx:PtxId}
!@!@! 2025.08.04T16:06:39.047+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 22.123 seconds (JVM running for 23.513) ${ctx:PtxId}
!@!@! 2025.08.04T16:07:01.422+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:01.422+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.994+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:07:25.995+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.125+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (129ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.126+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.126+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.126+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:07:26 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.126+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.126+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.127+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.128+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.151+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:183 ${ctx:PtxId}
!@!@! 2025.08.04T16:07:26.166+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:07:27.109+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:500 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.440+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.441+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.441+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.441+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.441+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.442+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.442+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.525+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (82ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.525+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.525+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.525+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:08:00 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.527+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:90 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:00.528+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:08:02.132+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:843 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.562+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (78ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:08:44 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.643+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.645+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:83 ${ctx:PtxId}
!@!@! 2025.08.04T16:08:44.645+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:08:55.574+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10447 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.562+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.565+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.722+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (156ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.723+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.724+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.724+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:09:03 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.724+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.724+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.725+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.725+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.729+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:168 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:03.731+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:09:33.116+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:28727 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.906+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.907+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.907+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.907+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.907+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.907+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.908+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.982+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (74ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.983+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.983+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.983+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:09:39 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.983+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.983+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.996+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.996+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.997+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:92 ${ctx:PtxId}
!@!@! 2025.08.04T16:09:38.997+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:09:51.312+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:11814 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.320+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.321+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.455+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (134ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.456+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.456+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.456+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:10:10 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.456+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.456+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.496+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.497+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.497+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:178 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:10.498+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:10:42.889+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:31791 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.555+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.555+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.555+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.556+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.556+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.556+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.556+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.628+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (70ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.629+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.630+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.631+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:10:50 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.632+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.632+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.641+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.642+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.645+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:95 ${ctx:PtxId}
!@!@! 2025.08.04T16:10:50.648+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:11:22.652+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:31308 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:22.666+0800 ERROR com.djcps.djeasyorder.common.log.EasyOrderErrorDeal - 捕获到未知异常 java.lang.NullPointerException: null
	at com.djcps.djeasyorder.smartbrief.model.vo.TaskResultVo.<init>(TaskResultVo.java:43) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl.pageQuery(TaskResultServiceImpl.java:41) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController.getList(TaskResultController.java:27) ~[classes/:?]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$FastClassBySpringCGLIB$$5db92096.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.smartbrief.controller.TaskResultController$$EnhancerBySpringCGLIB$$56add6b4.getList(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:209) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:136) ~[spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:891) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:991) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:925) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:981) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:884) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:661) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:858) [spring-webmvc-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:742) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52) [tomcat-embed-websocket-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:90) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HttpPutFormContentFilter.doFilterInternal(HttpPutFormContentFilter.java:109) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.filterAndRecordMetrics(WebMvcMetricsFilter.java:117) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:106) [spring-boot-actuator-2.0.7.RELEASE.jar:2.0.7.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107) [spring-web-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:198) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:493) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:140) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:81) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:87) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:800) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:806) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1498) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [?:1.8.0_442]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [?:1.8.0_442]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-8.5.35.jar:8.5.35]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.429+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.430+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.431+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.431+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.431+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.432+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.432+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.505+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (71ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:11:49 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.506+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.507+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.518+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.519+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.521+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:98 ${ctx:PtxId}
!@!@! 2025.08.04T16:11:49.523+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:11:50.652+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:641 ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.625+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.627+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.627+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.628+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.628+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.628+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.629+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.806+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (176ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.807+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.807+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.807+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:12:57 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.808+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.808+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.808+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.809+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.811+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:189 ${ctx:PtxId}
!@!@! 2025.08.04T16:12:57.814+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:12:58.768+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:462 ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.560+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.562+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.563+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.564+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.644+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (79ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.644+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.645+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.645+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:13:50 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.645+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.646+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.646+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.647+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.649+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:92 ${ctx:PtxId}
!@!@! 2025.08.04T16:13:50.652+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:13:51.656+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:517 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.436+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.437+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.438+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.438+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.438+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.439+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.439+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.601+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (161ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.602+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.602+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.603+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:14:52 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.603+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.603+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.603+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.604+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.605+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:170 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:52.607+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:14:53.495+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:397 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.891+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.891+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.891+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.892+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.892+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.892+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.892+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.962+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (69ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.963+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.963+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.964+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:15:00 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.964+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.964+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.976+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.976+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.977+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:87 ${ctx:PtxId}
!@!@! 2025.08.04T16:14:59.978+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:15:00.946+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:488 ${ctx:PtxId}
!@!@! 2025.08.04T16:20:42.185+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.04T16:20:45.319+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.04T16:20:55.260+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$b89d8dc3 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:03.482+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 23.278 seconds (JVM running for 24.627) ${ctx:PtxId}
!@!@! 2025.08.04T16:21:21.562+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:21.562+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.177+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.178+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (134ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:21:24 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.313+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.314+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.314+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.314+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.327+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:167 ${ctx:PtxId}
!@!@! 2025.08.04T16:21:24.334+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.302+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.303+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.303+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.304+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.304+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.305+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.305+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.370+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (64ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.371+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.371+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.372+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:22:05 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.372+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.372+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.373+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.373+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.375+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:76 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:05.377+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:22:06.987+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:1081 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.523+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.524+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.525+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] feignAccessUserInfo: {"accountId":"aa1b0017-b6ac-11e9-8613-000c299aa77b","casToken":"11f4fd2ee408498e80c4a8cd6dc06dd7","phone":"aa1b0017-b6ac-11e9-8613-000c299aa77b","system":"DJSUPPLIER_WEB","token":"11f4fd2ee408498e80c4a8cd6dc06dd7"} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.526+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.597+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- HTTP/1.1 200 OK (70ms) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.598+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] connection: keep-alive ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.598+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] content-type: application/json;charset=UTF-8 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.599+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] date: Mon, 04 Aug 2025 08:22:12 GMT ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.599+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] transfer-encoding: chunked ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.599+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.600+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"msg":"请求成功！","code":"10001200","data":{"total":-1,"list":[{"companyName":"温州东诚包装有限公司","companyId":"400","cooperatorStatus":"0"},{"companyName":"温州市新的百展包装装潢有限公司","companyId":"401","cooperatorStatus":"0"},{"companyName":"浙江宋氏实业有限公司","companyId":"563","cooperatorStatus":"0"},{"companyName":"青田三本包装有限公司","companyId":"606","cooperatorStatus":"0"},{"companyName":"郎和卓对接易网","companyId":"720","cooperatorStatus":"0"}]},"success":true} ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.600+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END HTTP (543-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.602+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:81 ${ctx:PtxId}
!@!@! 2025.08.04T16:22:12.603+0800 INFO  com.djcps.djeasyorder.common.config.SysInterceptor - 吴伟调用了/DJEASYORDER/result/page.do ${ctx:PtxId}
!@!@! 2025.08.04T16:22:14.052+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:923 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.717+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> POST http://**************:8080/djorg/getPartnerInfoById.org HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.718+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.718+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] Content-Length: 36 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.718+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById]  ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.718+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] {"companyIds":"720,401,400,606,563"} ${ctx:PtxId}
!@!@! 2025.08.04T17:09:05.719+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] ---> END HTTP (36-byte body) ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.726+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- ERROR ConnectTimeoutException: Connect to **************:8080 [/**************] failed: connect timed out (10006ms) ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.727+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] org.apache.http.conn.ConnectTimeoutException: Connect to **************:8080 [/**************] failed: connect timed out
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:151)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source)
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$6454604c.getUnionForSupplierId(<generated>)
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController.addCustomerForOrder(CustomerCommonController.java:218)
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController$$FastClassBySpringCGLIB$$8cc54f0a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController$$EnhancerBySpringCGLIB$$a44c5a6e.addCustomerForOrder(<generated>)
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer.lambda$syncOrderCustomer$0(OrderMqConsumer.java:64)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer.syncOrderCustomer(OrderMqConsumer.java:49)
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer$$FastClassBySpringCGLIB$$32bc1137.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer$$EnhancerBySpringCGLIB$$c74cb0a7.syncOrderCustomer(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114)
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 109 more
 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.728+0800 DEBUG com.djcps.djorg.organization.api.OrganizationManageApi - [OrganizationManageApi#getPartnerInfoById] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.730+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:10017 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.733+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:13774 ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.736+0800 ERROR com.djcps.djeasyorder.mq.consumer.OrderMqConsumer - ----------------------------------------消费失败---------------------------------------- ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.736+0800 ERROR com.djcps.djeasyorder.mq.consumer.OrderMqConsumer - 消息体:{"addressDetail":"双龙路199号南汇街道办事处一楼清风社区大厅(清风社区退役军人服务站)","customerId":"c174aa97-60b8-4ea6-bfe1-d977b516a474","customerName":"周志宣","lngAndLat":"120.680865,27.987864","supplierIdList":["400"],"townCode":"330302025","townName":"浙江省温州市鹿城区南汇街道","userArea":"3303"} ${ctx:PtxId}
!@!@! 2025.08.04T17:09:15.736+0800 ERROR com.djcps.djeasyorder.mq.consumer.OrderMqConsumer - Exception: feign.RetryableException: Connect to **************:8080 [/**************] failed: connect timed out executing POST http://**************:8080/djorg/getPartnerInfoById.org
	at feign.FeignException.errorExecuting(FeignException.java:65) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77) ~[feign-core-9.7.0.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102) ~[feign-core-9.7.0.jar:?]
	at com.sun.proxy.$Proxy156.getPartnerInfoById(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy157.getPartnerInfoById(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.common.utils.OrgApiUtil.getSupplierInfoByIdList(OrgApiUtil.java:278) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getSupplierInfoByIdList(UnionSupplierConfigServiceImpl.java:405) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:135) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getConfigSupplierList(UnionSupplierConfigServiceImpl.java:165) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:214) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getInfoById(UnionSupplierConfigServiceImpl.java:203) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl.getUnionForSupplierId(UnionSupplierConfigServiceImpl.java:630) ~[classes/:?]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$FastClassBySpringCGLIB$$596de28b.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.union.service.impl.UnionSupplierConfigServiceImpl$$EnhancerBySpringCGLIB$$6454604c.getUnionForSupplierId(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController.addCustomerForOrder(CustomerCommonController.java:218) ~[classes/:?]
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController$$FastClassBySpringCGLIB$$8cc54f0a.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.customer.controller.CustomerCommonController$$EnhancerBySpringCGLIB$$a44c5a6e.addCustomerForOrder(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer.lambda$syncOrderCustomer$0(OrderMqConsumer.java:64) ~[classes/:?]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[?:1.8.0_442]
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer.syncOrderCustomer(OrderMqConsumer.java:49) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer$$FastClassBySpringCGLIB$$32bc1137.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.OrderMqConsumer$$EnhancerBySpringCGLIB$$c74cb0a7.syncOrderCustomer(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: org.apache.http.conn.ConnectTimeoutException: Connect to **************:8080 [/**************] failed: connect timed out
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:151) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 97 more
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.PlainSocketImpl.socketConnect(Native Method) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206) ~[?:1.8.0_442]
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188) ~[?:1.8.0_442]
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392) ~[?:1.8.0_442]
	at java.net.Socket.connect(Socket.java:607) ~[?:1.8.0_442]
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 97 more
 ${ctx:PtxId}
