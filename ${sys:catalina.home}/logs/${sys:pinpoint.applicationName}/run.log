!@!@! 2025.08.05T10:00:09.874+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.05T10:00:13.445+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.05T10:00:23.497+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$96d315c2 ${ctx:PtxId}
!@!@! 2025.08.05T10:00:32.047+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 23.593 seconds (JVM running for 25.707) ${ctx:PtxId}
!@!@! 2025.08.05T10:00:49.865+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.05T10:00:49.866+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.076+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 接收到产品服务MQ消息:[{"id":"f43cb097-382d-49c9-8caa-3e554f3ba414","name":"宋氏5号(M136B)BC瓦参与活动","supplierId":"563","material":"B434B","layer":5,"edge":"BC瓦","operatorType":"edit"}] ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.084+0800 INFO  com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - 开始处理产品信息:{"edge":"BC瓦","id":"f43cb097-382d-49c9-8caa-3e554f3ba414","layer":5,"material":"B434B","name":"宋氏5号(M136B)BC瓦参与活动","operatorType":"edit","supplierId":"563"} ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.752+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do HTTP/1.1 ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.752+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Accept: application/json;charset=utf-8 ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.752+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] Content-Type: application/json ${ctx:PtxId}
!@!@! 2025.08.05T10:36:11.752+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] ---> END HTTP (0-byte body) ${ctx:PtxId}
!@!@! 2025.08.05T10:36:13.427+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- ERROR UnknownHostException: djproductserver: nodename nor servname provided, or not known (1674ms) ${ctx:PtxId}
!@!@! 2025.08.05T10:36:13.428+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] java.net.UnknownHostException: djproductserver: nodename nor servname provided, or not known
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867)
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302)
	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815)
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291)
	at java.net.InetAddress.getAllByName(InetAddress.java:1144)
	at java.net.InetAddress.getAllByName(InetAddress.java:1065)
	at org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102)
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212)
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$cb687b90.syncMqData(<generated>)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$67b622fc.productEdit(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181)
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114)
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188)
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355)
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77)
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042)
	at java.lang.Thread.run(Thread.java:750)
 ${ctx:PtxId}
!@!@! 2025.08.05T10:36:13.429+0800 DEBUG com.djcps.djeasyorder.feign.product.ProductFeign - [ProductFeign#getLayerList] <--- END ERROR ${ctx:PtxId}
!@!@! 2025.08.05T10:36:13.435+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:1687 ${ctx:PtxId}
!@!@! 2025.08.05T10:36:13.440+0800 ERROR com.djcps.djeasyorder.mq.consumer.ProductMqConsumer - MQ消费失败: feign.RetryableException: djproductserver: nodename nor servname provided, or not known executing POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do
	at feign.FeignException.errorExecuting(FeignException.java:65) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77) ~[feign-core-9.7.0.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102) ~[feign-core-9.7.0.jar:?]
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$cb687b90.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$67b622fc.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: java.net.UnknownHostException: djproductserver: nodename nor servname provided, or not known
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_442]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867) ~[?:1.8.0_442]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302) ~[?:1.8.0_442]
	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName(InetAddress.java:1144) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName(InetAddress.java:1065) ~[?:1.8.0_442]
	at org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more
 ${ctx:PtxId}
!@!@! 2025.08.05T10:56:02.075+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.05T10:56:05.277+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.05T10:56:14.881+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$fbabf746 ${ctx:PtxId}
!@!@! 2025.08.05T10:56:28.970+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 28.495 seconds (JVM running for 30.023) ${ctx:PtxId}
!@!@! 2025.08.05T10:56:55.680+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.05T10:56:55.680+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.05T11:29:06.916+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.05T11:29:10.039+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.05T11:29:19.567+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$e669048c ${ctx:PtxId}
!@!@! 2025.08.05T11:29:27.523+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 22.521 seconds (JVM running for 24.042) ${ctx:PtxId}
!@!@! 2025.08.05T11:29:44.488+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.05T11:29:44.488+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.05T11:30:57.695+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - The following profiles are active: aliyun-test ${ctx:PtxId}
!@!@! 2025.08.05T11:31:00.747+0800 INFO  com.djcps.djeasyorder.common.config.AsyncConfig - .........线程池加载成功，当前活跃线程数量:0 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:10.577+0800 INFO  com.djcps.djeasyorder.common.config.DataSourceConfig - PageHelperAutoConfiguration$$EnhancerBySpringCGLIB$$dd6d7347 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:17.857+0800 INFO  com.djcps.djeasyorder.DjeasyorderApplication - Started DjeasyorderApplication in 21.521 seconds (JVM running for 22.955) ${ctx:PtxId}
!@!@! 2025.08.05T11:31:30.751+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:473 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:30.969+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:159 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:33.803+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:219 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:33.907+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:96 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:34.816+0800 INFO  com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl - 【系统变量刷新完成】 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:34.816+0800 INFO  com.djcps.deploynotify.config.DeployJob - 【服务启动成功】 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:36.766+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:495 ${ctx:PtxId}
!@!@! 2025.08.05T11:31:36.918+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:143 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:25.085+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:543 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:25.344+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:241 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:28.685+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:485 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:31.956+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:224 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:32.477+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:512 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:32.742+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:252 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:35.456+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:345 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:36.909+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:212 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:37.426+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:508 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:37.647+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:208 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:40.335+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:382 ${ctx:PtxId}
!@!@! 2025.08.05T11:36:42.152+0800 INFO  com.djcps.log.entity.InfoLog - 执行时间:210 ${ctx:PtxId}
