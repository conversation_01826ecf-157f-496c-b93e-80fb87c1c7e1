!@!@! 2025.08.05T10:36:13.437+0800 === 方法运行失败,<PERSON>Key=null feign.RetryableException: djproductserver: nodename nor servname provided, or not known executing POST http://djproductserver/djproductserver/corrugatedManagement/getLayerList.do
	at feign.FeignException.errorExecuting(FeignException.java:65) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:105) ~[feign-core-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:77) ~[feign-core-9.7.0.jar:?]
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:102) ~[feign-core-9.7.0.jar:?]
	at com.sun.proxy.$Proxy194.getLayerList(Unknown Source) ~[?:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:343) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:197) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.operation.LogAspect.validIdentityAndSecure(LogAspect.java:77) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:212) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.sun.proxy.$Proxy195.getLayerList(Unknown Source) ~[?:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.getAllLayerInfo(RivalProductServiceImpl.java:1064) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl.syncMqData(RivalProductServiceImpl.java:1280) ~[classes/:?]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$FastClassBySpringCGLIB$$cb6fa182.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.rival.service.impl.RivalProductServiceImpl$$EnhancerBySpringCGLIB$$cb687b90.syncMqData(<generated>) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer.productEdit(ProductMqConsumer.java:50) ~[classes/:?]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$FastClassBySpringCGLIB$$541759d8.invoke(<generated>) ~[classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) ~[spring-core-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:746) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.log.ampq.ConsumerLog.consumerMessageLog(ConsumerLog.java:34) ~[djcpslog4j-2.2.0-20211210.065200-45.jar:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:174) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:92) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:185) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688) ~[spring-aop-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at com.djcps.djeasyorder.mq.consumer.ProductMqConsumer$$EnhancerBySpringCGLIB$$67b622fc.productEdit(<generated>) ~[classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_442]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_442]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_442]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_442]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:181) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.messaging.handler.invocation.InvocableHandlerMethod.invoke(InvocableHandlerMethod.java:114) ~[spring-messaging-5.0.11.RELEASE.jar:5.0.11.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.HandlerAdapter.invoke(HandlerAdapter.java:51) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.invokeHandler(MessagingMessageListenerAdapter.java:188) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.adapter.MessagingMessageListenerAdapter.onMessage(MessagingMessageListenerAdapter.java:126) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.doInvokeListener(AbstractMessageListenerContainer.java:1445) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.actualInvokeListener(AbstractMessageListenerContainer.java:1368) ~[spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.invokeListener(AbstractMessageListenerContainer.java:1355) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer.executeListener(AbstractMessageListenerContainer.java:1334) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.doReceiveAndExecute(SimpleMessageListenerContainer.java:817) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.receiveAndExecute(SimpleMessageListenerContainer.java:801) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer.access$700(SimpleMessageListenerContainer.java:77) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer$AsyncMessageProcessingConsumer.run(SimpleMessageListenerContainer.java:1042) [spring-rabbit-2.0.10.RELEASE.jar:2.0.10.RELEASE]
	at java.lang.Thread.run(Thread.java:750) [?:1.8.0_442]
Caused by: java.net.UnknownHostException: djproductserver: nodename nor servname provided, or not known
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:1.8.0_442]
	at java.net.InetAddress$2.lookupAllHostAddr(InetAddress.java:867) ~[?:1.8.0_442]
	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1302) ~[?:1.8.0_442]
	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:815) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName0(InetAddress.java:1291) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName(InetAddress.java:1144) ~[?:1.8.0_442]
	at java.net.InetAddress.getAllByName(InetAddress.java:1065) ~[?:1.8.0_442]
	at org.apache.http.impl.conn.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:45) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:112) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:394) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108) ~[httpclient-4.5.6.jar:4.5.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56) ~[httpclient-4.5.6.jar:4.5.6]
	at feign.httpclient.ApacheHttpClient.execute(ApacheHttpClient.java:85) ~[feign-httpclient-9.7.0.jar:?]
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:98) ~[feign-core-9.7.0.jar:?]
	... 71 more

