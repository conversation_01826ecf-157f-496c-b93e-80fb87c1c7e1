!@!@! 2025.08.04T15:03:49.082+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:49.285+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:49.497+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:49.704+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:49.908+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:50.116+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:50.330+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:50.540+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:50.747+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:50.965+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:51.181+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:51.390+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:51.598+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:51.815+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:52.022+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:52.231+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:52.438+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:52.646+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:52.855+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:53.063+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:53.273+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:03:53.485+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ef34eb3
!@!@! 2025.08.04T15:06:17.398+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@43039e62
!@!@! 2025.08.04T15:06:42.556+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@45fa180b
!@!@! 2025.08.04T15:07:30.858+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ca7edb3
!@!@! 2025.08.04T15:07:31.083+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ca7edb3
!@!@! 2025.08.04T15:11:12.143+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:12.351+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:12.558+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:12.765+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:12.975+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:13.189+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:13.400+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:13.623+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:13.833+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.043+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.248+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.390+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.530+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.734+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:14.944+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:15.163+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:15.374+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:15.580+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:15.800+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:16.011+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:16.218+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:11:16.425+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@381c826c
!@!@! 2025.08.04T15:12:04.478+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7912b3
!@!@! 2025.08.04T15:12:04.685+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7912b3
!@!@! 2025.08.04T15:13:31.116+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:31.225+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.203+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.297+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.388+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.561+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.770+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:32.975+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:33.181+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:33.388+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:33.598+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:33.814+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:34.022+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:34.228+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:34.399+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:34.608+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:34.820+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:35.029+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:35.287+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:35.493+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:35.700+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:35.910+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:36.118+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:36.284+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:40.011+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:13:40.218+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@188971cf
!@!@! 2025.08.04T15:17:51.044+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7c6ceba4
!@!@! 2025.08.04T15:17:51.145+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7c6ceba4
!@!@! 2025.08.04T15:18:02.006+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.104+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.245+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.352+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.396+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1ec48a5
!@!@! 2025.08.04T15:18:02.446+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.539+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1ec48a5
!@!@! 2025.08.04T15:18:02.554+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.679+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:02.885+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:03.109+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:03.323+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:03.430+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:03.593+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:03.799+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:04.025+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:04.239+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:04.413+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:04.636+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:04.844+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:05.058+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:05.268+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:05.449+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:18:05.659+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@20ee660b
!@!@! 2025.08.04T15:20:11.073+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ba45f47
!@!@! 2025.08.04T15:20:11.282+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ba45f47
!@!@! 2025.08.04T15:20:12.280+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ba45f47
!@!@! 2025.08.04T15:20:12.559+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7ba45f47
!@!@! 2025.08.04T15:21:08.026+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:08.224+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:08.379+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:08.518+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:08.730+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:08.942+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:09.169+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:09.396+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:09.605+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:09.812+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:10.026+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:10.192+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:10.404+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:10.613+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:10.823+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:11.062+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:11.267+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:11.488+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:11.700+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:11.903+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:12.113+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:12.291+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5dd5422f
!@!@! 2025.08.04T15:21:26.077+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@405f01f9
!@!@! 2025.08.04T15:21:26.239+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@405f01f9
!@!@! 2025.08.04T15:21:27.243+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@405f01f9
!@!@! 2025.08.04T15:21:27.489+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@405f01f9
!@!@! 2025.08.04T15:25:08.293+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5acf4310
!@!@! 2025.08.04T15:25:08.501+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5acf4310
!@!@! 2025.08.04T15:25:09.502+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5acf4310
!@!@! 2025.08.04T15:25:09.783+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5acf4310
!@!@! 2025.08.04T15:26:23.320+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:23.436+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.336+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.441+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.538+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.679+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.827+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:26.943+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@77b93ce3
!@!@! 2025.08.04T15:26:26.985+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.057+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@77b93ce3
!@!@! 2025.08.04T15:26:27.071+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.198+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.323+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.460+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.586+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:27.792+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.002+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.216+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.359+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.460+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.674+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:28.883+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:29.090+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:29.294+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:29.500+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:26:29.663+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@22168ed2
!@!@! 2025.08.04T15:29:51.035+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T15:29:51.244+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T15:29:52.239+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T15:29:52.480+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T15:31:00.021+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:00.232+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:00.520+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:00.830+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:01.048+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:01.254+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:01.462+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:01.672+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:01.882+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:02.136+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:02.343+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:02.548+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:02.757+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:02.966+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.172+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.339+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.438+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.647+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.854+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:03.945+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:04.059+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:31:04.155+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6b6bae60
!@!@! 2025.08.04T15:36:20.772+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4b3d2267
!@!@! 2025.08.04T15:36:21.065+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4b3d2267
!@!@! 2025.08.04T15:36:22.710+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@64f7994f
!@!@! 2025.08.04T15:36:23.082+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@64f7994f
!@!@! 2025.08.04T15:44:26.554+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5fba0302
!@!@! 2025.08.04T15:44:26.648+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5fba0302
!@!@! 2025.08.04T15:44:39.415+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:39.513+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:39.656+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:39.809+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:39.911+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@47e36392
!@!@! 2025.08.04T15:44:39.960+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.094+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@47e36392
!@!@! 2025.08.04T15:44:40.108+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.249+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.377+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.523+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.695+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:40.874+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:41.285+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:41.564+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:41.915+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:42.136+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:42.507+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:42.799+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:43.047+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:43.313+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:43.522+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:43.830+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:44:44.095+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4bc49d70
!@!@! 2025.08.04T15:59:35.951+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@48d6cf48
!@!@! 2025.08.04T15:59:36.161+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@48d6cf48
!@!@! 2025.08.04T15:59:37.182+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@48d6cf48
!@!@! 2025.08.04T15:59:37.462+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@48d6cf48
!@!@! 2025.08.04T16:00:47.925+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6d8cb3fe
!@!@! 2025.08.04T16:00:48.125+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6d8cb3fe
!@!@! 2025.08.04T16:00:48.995+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6d8cb3fe
!@!@! 2025.08.04T16:00:49.290+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6d8cb3fe
!@!@! 2025.08.04T16:01:09.892+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@126e0f15
!@!@! 2025.08.04T16:01:10.110+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@126e0f15
!@!@! 2025.08.04T16:01:11.061+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@126e0f15
!@!@! 2025.08.04T16:01:11.350+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@126e0f15
!@!@! 2025.08.04T16:01:48.365+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1e9f5971
!@!@! 2025.08.04T16:01:48.538+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1e9f5971
!@!@! 2025.08.04T16:01:49.582+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1e9f5971
!@!@! 2025.08.04T16:01:49.882+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@1e9f5971
!@!@! 2025.08.04T16:02:43.869+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5560d51b
!@!@! 2025.08.04T16:02:43.963+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5560d51b
!@!@! 2025.08.04T16:02:53.932+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.028+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.174+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.330+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.391+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4256c76c
!@!@! 2025.08.04T16:02:54.440+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.521+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4256c76c
!@!@! 2025.08.04T16:02:54.535+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.665+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.795+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:54.901+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:55.132+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:55.302+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:55.511+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:55.726+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:55.936+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:56.150+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:56.319+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:56.527+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:56.734+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:56.942+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:57.152+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:57.317+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:02:57.567+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@28901fe7
!@!@! 2025.08.04T16:04:24.811+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@471daad9
!@!@! 2025.08.04T16:04:24.912+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@471daad9
!@!@! 2025.08.04T16:04:37.022+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.125+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.486+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.592+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.607+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6edac304
!@!@! 2025.08.04T16:04:37.694+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.741+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6edac304
!@!@! 2025.08.04T16:04:37.794+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:37.920+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.054+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.157+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.274+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.417+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.584+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:38.798+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:39.015+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:39.224+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:39.394+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:39.607+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:39.843+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:40.078+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:40.287+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:40.550+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:04:40.794+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@46059b73
!@!@! 2025.08.04T16:05:24.351+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ae52f7b
!@!@! 2025.08.04T16:05:24.557+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ae52f7b
!@!@! 2025.08.04T16:05:25.460+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ae52f7b
!@!@! 2025.08.04T16:05:25.743+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ae52f7b
!@!@! 2025.08.04T16:06:04.786+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5bd9049e
!@!@! 2025.08.04T16:06:04.953+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5bd9049e
!@!@! 2025.08.04T16:06:06.100+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ec7cd54
!@!@! 2025.08.04T16:06:06.376+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2ec7cd54
!@!@! 2025.08.04T16:06:54.925+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:55.243+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:55.592+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:55.832+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:56.041+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:56.248+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:56.484+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:56.711+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:57.006+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:57.267+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:57.624+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:57.894+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:58.223+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:58.454+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:58.728+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:59.043+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:59.354+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:59.680+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:06:59.941+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:07:00.269+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:07:00.617+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:07:01.185+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@57f1fbae
!@!@! 2025.08.04T16:07:25.027+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6fdee9fd
!@!@! 2025.08.04T16:07:25.195+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6fdee9fd
!@!@! 2025.08.04T16:07:26.184+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6fdee9fd
!@!@! 2025.08.04T16:07:26.427+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6fdee9fd
!@!@! 2025.08.04T16:07:58.973+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@23882573
!@!@! 2025.08.04T16:07:59.304+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@23882573
!@!@! 2025.08.04T16:08:00.598+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@23882573
!@!@! 2025.08.04T16:08:01.075+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@23882573
!@!@! 2025.08.04T16:08:43.564+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@52dddc81
!@!@! 2025.08.04T16:08:43.774+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@52dddc81
!@!@! 2025.08.04T16:08:44.712+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@52dddc81
!@!@! 2025.08.04T16:08:44.988+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@52dddc81
!@!@! 2025.08.04T16:09:02.200+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@53e4b738
!@!@! 2025.08.04T16:09:02.482+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@53e4b738
!@!@! 2025.08.04T16:09:03.779+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@53e4b738
!@!@! 2025.08.04T16:09:04.114+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@53e4b738
!@!@! 2025.08.04T16:09:37.972+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4a7de414
!@!@! 2025.08.04T16:09:38.180+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4a7de414
!@!@! 2025.08.04T16:09:39.051+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4a7de414
!@!@! 2025.08.04T16:09:39.358+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@4a7de414
!@!@! 2025.08.04T16:10:09.237+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@71618b71
!@!@! 2025.08.04T16:10:09.446+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@71618b71
!@!@! 2025.08.04T16:10:10.630+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@71618b71
!@!@! 2025.08.04T16:10:10.933+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@71618b71
!@!@! 2025.08.04T16:10:49.599+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T16:10:49.811+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T16:10:50.702+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T16:10:51.034+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@6f731ae9
!@!@! 2025.08.04T16:11:48.380+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2de70f18
!@!@! 2025.08.04T16:11:48.560+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2de70f18
!@!@! 2025.08.04T16:11:49.575+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2de70f18
!@!@! 2025.08.04T16:11:49.862+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@2de70f18
!@!@! 2025.08.04T16:12:56.574+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@771ae466
!@!@! 2025.08.04T16:12:56.781+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@771ae466
!@!@! 2025.08.04T16:12:57.883+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@771ae466
!@!@! 2025.08.04T16:12:58.191+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@771ae466
!@!@! 2025.08.04T16:13:49.605+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@21f13743
!@!@! 2025.08.04T16:13:49.814+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@21f13743
!@!@! 2025.08.04T16:13:50.712+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@21f13743
!@!@! 2025.08.04T16:13:50.987+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@21f13743
!@!@! 2025.08.04T16:14:51.440+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@e5dea8a
!@!@! 2025.08.04T16:14:51.647+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@e5dea8a
!@!@! 2025.08.04T16:14:52.669+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@e5dea8a
!@!@! 2025.08.04T16:14:52.952+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@e5dea8a
!@!@! 2025.08.04T16:14:58.873+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7edd1552
!@!@! 2025.08.04T16:14:59.087+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7edd1552
!@!@! 2025.08.04T16:15:00.031+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7edd1552
!@!@! 2025.08.04T16:15:00.314+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@7edd1552
!@!@! 2025.08.04T16:21:16.899+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:17.123+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:17.338+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:17.544+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:17.750+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:17.957+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:18.170+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:18.334+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:18.487+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:18.699+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:18.906+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:19.120+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:19.332+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:19.540+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:19.745+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:19.957+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:20.167+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:20.399+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:20.643+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:20.893+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:21.129+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:21.421+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:22.857+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:23.124+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:24.378+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:21:24.618+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@5578ed7e
!@!@! 2025.08.04T16:22:04.279+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:04.489+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:05.447+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:05.757+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:11.486+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:11.698+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:12.665+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
!@!@! 2025.08.04T16:22:12.975+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@423e680
