!@!@! 2025.08.05T10:00:44.869+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:45.053+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:45.241+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:45.430+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:45.626+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:45.819+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:46.005+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:46.190+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:46.420+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:46.979+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:47.240+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:47.820+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.008+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.197+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.386+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.586+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.775+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:48.965+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:49.154+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:49.350+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:49.546+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:00:49.741+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@13563e64
!@!@! 2025.08.05T10:56:51.303+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:51.490+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:51.691+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:51.891+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:52.101+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:52.269+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:52.465+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:52.669+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:52.871+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:53.063+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:53.273+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:53.526+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:53.772+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:53.956+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:54.149+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:54.322+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:54.522+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:54.742+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:54.944+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:55.139+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:55.363+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T10:56:55.515+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@655d5285
!@!@! 2025.08.05T11:29:40.632+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:40.748+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:40.817+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.004+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.218+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.406+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.594+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.783+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:41.972+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.173+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.363+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.520+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.684+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.871+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:42.986+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:43.172+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:43.359+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:43.545+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:43.740+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:43.934+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:44.162+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:29:44.356+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@75c095a5
!@!@! 2025.08.05T11:31:30.733+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:30.846+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:30.953+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:31.146+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:31.355+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:31.568+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:31.822+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:32.034+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:32.250+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:32.560+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:32.815+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.066+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.274+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.469+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.628+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.708+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.788+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.867+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:33.980+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:34.196+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:34.405+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
!@!@! 2025.08.05T11:31:34.599+0800 === djcpsRedisLogger
djcpsRedisLogger==> Redis_Preparing: Jedis com.djcps.redis.client.RedisClientSingle.getJedis() 
djcpsRedisLogger==> Redis_Parameters:  
djcpsRedisLogger<== Redis_Result: redis.clients.jedis.Jedis@606d2a92
