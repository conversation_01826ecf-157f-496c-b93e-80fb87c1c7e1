# TaskResult API 接口文档

## 1. 删除任务结果接口

**接口地址：** `POST /result/delete.do`

**功能描述：** 根据给定参数删除对应的task_result记录和对应的task_result_item记录

**请求参数：**
```json
{
    "skey": "400",
    "taskId": 123,
    "dataDate": "2025-07-31",
    "cycleType": "day",
    "supplierId": "400"
}
```

**参数说明：**
- `skey`: 搜索关键字（必填）
- `taskId`: 任务ID（必填）
- `dataDate`: 数据日期（必填）
- `cycleType`: 周期类型（必填）
- `supplierId`: 供应商ID（必填）

**响应示例：**
```json
{
    "code": "10001200",
    "data": "删除成功",
    "msg": "请求成功！",
    "success": true
}
```

## 2. 保存任务结果子项接口

**接口地址：** `POST /result/saveItem.do`

**功能描述：** 保存task_result_item记录

**请求参数：**
```json
{
    "resultId": 1,
    "taskId": 123,
    "templateId": 456,
    "originData": "原始数据内容",
    "skey": "400",
    "prompt": "提示词内容",
    "subBizType": "summary",
    "batchNo": "batch_001"
}
```

**参数说明：**
- `resultId`: 结果ID（必填）
- `taskId`: 任务ID（必填）
- `templateId`: 模板ID（必填）
- `originData`: 原始数据（必填）
- `skey`: 搜索关键字（必填）
- `prompt`: 提示词（必填）
- `subBizType`: 子业务类型（必填）
- `batchNo`: 批次号（必填）

**响应示例：**
```json
{
    "code": "10001200",
    "data": 789,
    "msg": "请求成功！",
    "success": true
}
```

## 3. 更新任务结果子项接口

**接口地址：** `POST /result/updateItem.do`

**功能描述：** 更新task_result_item中指定id的item_result字段

**请求参数：**
```json
{
    "taskItemId": 789,
    "analysisResult": "分析结果内容"
}
```

**参数说明：**
- `taskItemId`: 任务子项ID（必填）
- `analysisResult`: 分析结果（必填）

**响应示例：**
```json
{
    "code": "10001200",
    "data": "更新成功",
    "msg": "请求成功！",
    "success": true
}
```

## 4. 保存任务结果接口

**接口地址：** `POST /result/save.do`

**功能描述：** 保存任务结果记录到task_result表中，返回task_result的id字段

**请求参数：**
```json
{
    "skey": "400",
    "taskId": 123,
    "dataDate": "2025-07-31",
    "bizType": "deliver",
    "cycleType": "day",
    "execStartTime": "2025-08-05T10:00:00",
    "batchNo": "batch_001",
    "execEndTime": "2025-08-05T11:00:00",
    "userId": "user123",
    "orgId": "orgId_a7f2d136c681",
    "supplierId": "400",
    "role": "salesman"
}
```

**参数说明：**
- `skey`: 搜索关键字（必填）
- `taskId`: 任务ID（必填）
- `dataDate`: 数据日期（必填）
- `bizType`: 业务类型（必填）
- `cycleType`: 周期类型（必填）
- `execStartTime`: 执行开始时间（可选）
- `batchNo`: 批次号（必填）
- `execEndTime`: 执行结束时间（可选）
- `userId`: 用户ID（可选）
- `orgId`: 组织ID（可选）
- `supplierId`: 供应商ID（必填）
- `role`: 角色（可选）

**响应示例：**
```json
{
    "code": "10001200",
    "data": 123,
    "msg": "请求成功！",
    "success": true
}
```

## 5. 现有分页查询接口

**接口地址：** `POST /result/page.do`

**功能描述：** 分页查询任务结果

**请求参数：**
```json
{
    "skey": "400",
    "supplierId": "400",
    "orgId": "orgId_a7f2d136c681",
    "role": "role_1dcc6b20582d",
    "cycleType": "day",
    "bizType": "deliver",
    "pageNo": 1,
    "pageSize": 10
}
```

**响应示例：**
```json
{
    "code": "10001200",
    "data": {
        "total": 1,
        "list": [
            {
                "createTime": "2025-08-01T15:29:31",
                "dataDate": "2025-07-31"
            }
        ]
    },
    "msg": "请求成功！",
    "success": true
}
```
