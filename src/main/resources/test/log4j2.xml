<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<Properties>
		<!-- 定义项目存储日志文件夹 -->
		<Property name="LOG_HOME">${sys:catalina.home}/logs/${sys:pinpoint.applicationName}</Property>
	</Properties>

	<Appenders>
		<!-- 运行日志输出文件 -->
		<RollingRandomAccessFile name="run"
			fileName="${LOG_HOME}/run.log" filePattern="${LOG_HOME}/run.%d{yyyy-MM-dd}-%i.log.gz"
			append="true">
			<!--日志格式 -->
			<PatternLayout
				pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} %-5level %c - %msg%xEx%n" />
			<Policies>
				<!-- 对应 filePattern维度(根据filePattern的正则来)，此处为天数 -->
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
				<!-- 文件大小单位为字节，默认不限制 -->
				<SizeBasedTriggeringPolicy size="1073741824" />
			</Policies>
			<!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
			<DefaultRolloverStrategy max="5">
				<Delete basePath="${LOG_HOME}" maxDepth="1">
					<IfFileName glob="run.*.log.gz" />
					<IfLastModified age="2d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>

		<!-- 操作日志输出文件 -->
		<RollingRandomAccessFile name="operation"
			fileName="${LOG_HOME}/operation.log" filePattern="${LOG_HOME}/operation.%d{yyyy-MM-dd}-%i.log.gz"
			append="true">
			<PatternLayout
				pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n" />
			<Policies>
				<!-- 对应 filePattern维度，此处为天数 -->
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
				<!-- 文件大小单位为字节，默认不限制 -->
				<SizeBasedTriggeringPolicy size="1073741824" />
			</Policies>
			<!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
			<DefaultRolloverStrategy max="5">
				<Delete basePath="${LOG_HOME}" maxDepth="1">
					<IfFileName glob="operation.*.log.gz" />
					<IfLastModified age="2d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>
		
		<!-- 错误日志打印 -->
		<RollingRandomAccessFile name="error"
			fileName="${LOG_HOME}/error.log" filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}-%i.log.gz"
			append="true">
			<!--日志格式 -->
			<PatternLayout pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n" />
			<Policies>
				<!-- 对应 filePattern维度(根据filePattern的正则来)，此处为天数 -->
				<TimeBasedTriggeringPolicy interval="1"
					modulate="true" />
				<!-- 文件大小单位为字节，默认不限制 -->
				<SizeBasedTriggeringPolicy size="1073741824" />
			</Policies>
			<!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
			<DefaultRolloverStrategy max="5">
				<Delete basePath="${LOG_HOME}" maxDepth="1">
					<IfFileName glob="error.*.log.gz" />
					<IfLastModified age="2d" />
				</Delete>
			</DefaultRolloverStrategy>
		</RollingRandomAccessFile>
		
		<!-- http日志输出文件 -->
        <RollingRandomAccessFile name="http"
            fileName="${LOG_HOME}/http.log" filePattern="${LOG_HOME}/http.%d{yyyy-MM-dd}-%i.log.gz"
            append="true">
            <PatternLayout
                pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n" />
            <Policies>
                <!-- 对应 filePattern维度，此处为天数 -->
                <TimeBasedTriggeringPolicy interval="1"
                    modulate="true" />
                <!-- 文件大小单位为字节，默认不限制 -->
                <SizeBasedTriggeringPolicy size="1073741824" />
            </Policies>
            <!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="http.*.log.gz" />
                    <IfLastModified age="2d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        
            
        <!-- rabbitMQ日志输出文件 -->
        <RollingRandomAccessFile name="rabbitMQ"
            fileName="${LOG_HOME}/rabbitMQ.log" filePattern="${LOG_HOME}/rabbitMQ.%d{yyyy-MM-dd}-%i.log.gz"
            append="true">
            <PatternLayout
                pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n" />
            <Policies>
                <!-- 对应 filePattern维度，此处为天数 -->
                <TimeBasedTriggeringPolicy interval="1"
                    modulate="true" />
                <!-- 文件大小单位为字节，默认不限制 -->
                <SizeBasedTriggeringPolicy size="1073741824" />
            </Policies>
            <!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="rabbitMQ.*.log.gz" />
                    <IfLastModified age="2d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        
        
         <!-- redis日志输出文件 -->
        <RollingRandomAccessFile name="redis"
            fileName="${LOG_HOME}/redis.log" filePattern="${LOG_HOME}/redis.%d{yyyy-MM-dd}-%i.log.gz"
            append="true">
            <PatternLayout
                pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n" />
            <Policies>
                <!-- 对应 filePattern维度，此处为天数 -->
                <TimeBasedTriggeringPolicy interval="1"
                    modulate="true" />
                <!-- 文件大小单位为字节，默认不限制 -->
                <SizeBasedTriggeringPolicy size="1073741824" />
            </Policies>
            <!-- 同一天的日志最大扩展个数为5，最多保存7天 -->
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="redis.*.log.gz" />
                    <IfLastModified age="2d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        
        
	</Appenders>

	<Loggers>
		<!-- 项目统一运行日志输出配置 -->
		<Logger name="com.mvc" level="DEBUG" additivity="false">
			<appender-ref ref="run" />
		</Logger>
		<!-- HTTP打印日志 -->
		<Logger name="rpc.plugin.http.log.HttpLogger" additivity="false"
			level="DEBUG">
			<appender-ref ref="http" />
		</Logger>
		<!-- aop操作日志输出配置 -->
		<Logger name="com.mvc.log.operation.LogAspect"
			level="INFO" additivity="false">
			<appender-ref ref="operation" />
		</Logger>
		<!-- 错误日志打印 -->
		<Logger level="ERROR" name="com.djcps.log.entity.ErrorLog"
			additivity="false">
			<appender-ref ref="error" />
		</Logger>
		<!-- MQ日志打印 -->
        <Logger level="debug" name="com.djcps.log.ampq.ConsumerLog"
            additivity="false">
            <appender-ref ref="rabbitMQ" />
        </Logger>
        <Logger level="debug" name="com.djcps.log.ampq.ProducerLog"
            additivity="false">
            <appender-ref ref="rabbitMQ" />
        </Logger>
        <!-- redis日志打印 -->
        <Logger level="debug" name="com.djcps.redis.log.RedisLog"
            additivity="false">
            <appender-ref ref="redis" />
        </Logger>
		
	</Loggers>
</configuration>