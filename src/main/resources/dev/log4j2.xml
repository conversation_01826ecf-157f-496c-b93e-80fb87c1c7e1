<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <Properties>
        <Property name="LOG_HOME">${sys:catalina.home}/logs/${sys:pinpoint.applicationName}</Property>
    </Properties>
    <Appenders>
        <RollingRandomAccessFile name="run"
                                 fileName="${LOG_HOME}/run.log" filePattern="${LOG_HOME}/run.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} %-5level %c - %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="run.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="operation"
                                 fileName="${LOG_HOME}/operation.log"
                                 filePattern="${LOG_HOME}/operation.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="operation.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="error"
                                 fileName="${LOG_HOME}/error.log"
                                 filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="run.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="http"
                                 fileName="${LOG_HOME}/http.log" filePattern="${LOG_HOME}/http.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="http.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="rabbitMQ"
                                 fileName="${LOG_HOME}/rabbitMQ.log"
                                 filePattern="${LOG_HOME}/rabbitMQ.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="rabbitMQ.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="redis"
                                 fileName="${LOG_HOME}/redis.log"
                                 filePattern="${LOG_HOME}/redis.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="!@!@! %d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} === %msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="redis.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="mongoAppender"
                                 fileName="${LOG_HOME}/mongoAppender.log"
                                 filePattern="${LOG_HOME}/mongoAppender.%d{yyyy-MM-dd}-%i.log.gz"
                                 append="true">
            <PatternLayout
                    pattern="%msg%xEx%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
                <SizeBasedTriggeringPolicy size="1073741824"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="mongoAppender.*.log.gz"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <Kafka name="Kafka" topic="pinpoint-log" syncSend="false">
            <PatternLayout
                    pattern="{\n &quot;thread&quot; : &quot;[%t]&quot;,\n &quot;level&quot; : &quot;%-5level&quot;,\n &quot;loggerName&quot; : &quot;%-20c&quot;,\n
&quot;message&quot; : &quot;${sys:pinpoint.applicationName} %d{HH:mm:ss.SSS} %-5level %c{10} %M - %enc{%msg}{JSON}%xEx{none}%enc{%xEx}{JSON}&quot;,\n
&quot;transactionId&quot; : &quot;${ctx:PtxId}&quot;,\n &quot;spanId&quot; : &quot;${ctx:PspanId}&quot;,\n &quot;applicationName&quot; : &quot;${sys:pinpoint.applicationName}&quot;,\n &quot;timestamp&quot; : &quot;%d{COMPACT}&quot;\n}"/>
            <Property name="bootstrap.servers">kafka-0.kafka-svc:9092,kafka-1.kafka-svc:9092,kafka-2.kafka-svc:9092
            </Property>
            <Property name="max.block.ms">2000</Property>
        </Kafka>
        <Async name="Kafka-Async">
            <AppenderRef ref="Kafka"/>
        </Async>
        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="%d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} %-5level %c - %msg%xEx%n"/>
        </Console>
        <DingTalk name="dingTalkAppender"
                  webhook="https://oapi.dingtalk.com/robot/send?access_token=e5a2c0f813194d64b9ec01a78dd66b09f68e5bd1315452e90ac0544b63b1f9d1"
                  secret="SEC64883b202e8abe44e2a3437a053edbbafac560f4731df4dd5ecd9cbb0f52a672">
            控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） 
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyy.MM.dd}T%d{HH:mm:ss.SSSZ} %-5level %c - %msg%xEx%n" disableAnsi="false"/>
        </DingTalk>
    </Appenders>
    <Loggers>
        <Root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
            <AppenderRef ref="dingTalkAppender"/>
        </Root>
        <Logger name="com.djcps" level="DEBUG" additivity="false">
            <appender-ref ref="run"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger name="com.djcloud" level="DEBUG" additivity="false">
            <appender-ref ref="run"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger name="com.djcscl" level="DEBUG" additivity="false">
            <appender-ref ref="run"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger name="rpc.plugin.http.log.HttpLogger" additivity="false"
                level="DEBUG">
            <appender-ref ref="http"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger name="com.djcps.log.operation.LogAspect"
                level="INFO" additivity="false">
            <appender-ref ref="operation"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger level="ERROR" name="com.djcps.log.entity.ErrorLog"
                additivity="false">
            <appender-ref ref="error"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger level="debug" name="com.djcps.log.ampq.ConsumerLog"
                additivity="false">
            <appender-ref ref="rabbitMQ"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger level="debug" name="com.djcps.log.ampq.ProducerLog"
                additivity="false">
            <appender-ref ref="rabbitMQ"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger level="debug" name="com.djcps.redis.log.RedisLog"
                additivity="false">
            <appender-ref ref="redis"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
        <Logger name="com.djcps.log.util.OperationUtils" level="DEBUG" additivity="false">
            <AppenderRef ref="mongoAppender"/>
            <appender-ref ref="console"/>
            <appender-ref ref="Kafka-Async"/>
        </Logger>
    </Loggers>
</configuration>