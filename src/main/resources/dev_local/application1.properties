#oa \u4E3B\u5E93
spring.datasource.hikari.jdbc-url=**********************************************************************************************************************************
spring.datasource.hikari.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.hikari.username=djoa
spring.datasource.hikari.password=dongjingoa
#customer \u7CBE\u7EC6\u5316\u5E93
#\u591A\u6570\u636E\u6E90\u65F6\uFF0C\u4E3Ajdbc-url
spring.datasource.customer.hikari.jdbc-url=****************************************************************************************************************************************
spring.datasource.customer.hikari.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.customer.hikari.username=djoa
spring.datasource.customer.hikari.password=dongjingoa
spring.datasource.hikari.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.max-wait-millis=10000
spring.datasource.hikari.min-idle=5
spring.datasource.hikari.initial-size=5
spring.datasource.hikari.connection-properties=characterEncoding=utf8
spring.datasource.hikari.connectionInitSqls=set names utf8mb4;
spring.datasource.hikari.pool-name=DatebookHikariCP
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
spring.http.encoding.force-response=true
server.servlet.context-path=/DJEASYORDER
server.port=8087
#    spring aop
spring.aop.auto=true

ignoredUrl=.*Common\\..*,.*login\\..*
spring.mvc.pathmatch.use-registered-suffix-pattern=true
#mq
rabbitUsername=djmq
rabbitPassword=dongjingmq
rabbitHost=***********
rabbitPort=23923

#redis
dj.redis.open-cluster=false
dj.redis.single-redis.database=5
dj.redis.single-redis.host=***********
dj.redis.single-redis.password=djredis
dj.redis.single-redis.port=24916
dj.redis.single-redis.timeout=6000
loginURL=http://***********:38091/djauth/
orgURL=http://***********:24323/djorg/
djoaUrl=http://***********:22762/DJOA
fileIpAddress=http://***********7/DJOA/
#crm url
crmUrl=http://***********:29146/djcrm/
#\u7EBF\u7D22\u6A21\u677F\u7684\u5730\u5740
clueTemplateUrl=https://oa.djcps.com/DJOA/img0/M00/02/7C/rBAC-l6ir-2ANTerAAA9uA0WeXw79.xlsx
#\u5927\u5BA2\u6237\u670D\u52A1
dj.djcssservice.url=http://***********:23900/djcssservice/
#\u8BA2\u5355\u670D\u52A1
dj.orderserver.url=http://***********:22904/djorderserver/
#\u5730\u5740\u670D\u52A1
dj.address.url=http://***********:26961/djaddressserver/
#\u4EA7\u54C1\u670D\u52A1
dj.product.url=http://***********:34812/djproductserver/
#\u5927\u6570\u636E\u670D\u52A1
dj.bigdata.url=http://***********:26432/djbigdata/
#\u9A7E\u9A76\u8231
dj.cockpit.url=http://***********:21068/djcockpit/
#\u6587\u4EF6\u670D\u52A1
fileServer=http\://***********:27727/djfileserver/
#first dfs
fdfsUrl=***********\:22122
#mongodb
spring.data.mongodb.uri=*******************************************************
dataServer.appKey=LuCICohO
dj.marketing.url=http://***********:29247/djpmarketing

openapiUrl=https://aim.jiangfanyi.top/ai/openapi/