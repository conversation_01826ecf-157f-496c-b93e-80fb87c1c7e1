package com.djcps.djeasyorder.addressupdaterecord.model.po;

import com.djcps.djeasyorder.clue.model.base.AddClueBaseBo;
import com.djcps.djeasyorder.clue.model.base.GetClueBaseInfoVo;
import com.djcps.djeasyorder.common.model.UserModel;
import com.djcps.djeasyorder.common.utils.LambdaUtil;
import com.djcps.djeasyorder.common.utils.UUIDUtil;
import com.djcps.djeasyorder.customer.enums.BelongTypeEnum;
import com.djcps.djeasyorder.customer.model.po.expand.Customer;
import lombok.Data;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @create 2022-12-07 14:15
 */
@Data
public class InsertCrmAddressUpdateRecordPo {
    /**
     * 唯一id
     */
    private String id;

    /**
     * 拆分键
     */
    private String skey;

    /**
     * 客户/线索id
     */
    private String infoId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 所属类型(0-客户,1-线索)
     */
    private Integer belongType;

    /**
     * 修改人id
     */
    private String modifierId;

    /**
     * 修改人名字
     */
    private String modifierName;

    /**
     * 修改前详细地址
     */
    private String oldAddressDetail;

    /**
     * 修改前客户经纬度
     */
    private String oldLongAndLat;

    /**
     * 修改后详细地址
     */
    private String modifyAddressDetail;

    /**
     * 修改后客户经纬度
     */
    private String modifyLongAndLat;

    public InsertCrmAddressUpdateRecordPo() {
    }

    public InsertCrmAddressUpdateRecordPo(AddClueBaseBo clueInfo, GetClueBaseInfoVo oldInfo, UserModel userInfo) {
        this.id = UUIDUtil.getUUID();
        this.skey = userInfo.getUcompany();
        this.infoId = oldInfo.getClueId();
        this.customerName = oldInfo.getCustomerName();
        this.belongType = BelongTypeEnum.线索.getCode();
        this.modifierId = userInfo.getId();
        this.modifierName = userInfo.getUname();
        this.oldAddressDetail = oldInfo.getCustomerArea() + oldInfo.getDetailAddress();
        this.oldLongAndLat = oldInfo.getLongitudeAndLatitudeStr();
        this.modifyAddressDetail = clueInfo.getCustomerArea() + clueInfo.getDetailAddressModel().getDetailAddress();
        if (ObjectUtils.isEmpty(clueInfo.getLongitudeAndLatitudeStr())) {
            this.modifyLongAndLat = "[]";
        } else {
            this.modifyLongAndLat = clueInfo.getLongitudeAndLatitudeStr();
        }
    }

    public InsertCrmAddressUpdateRecordPo(Customer customer, Customer oldInfo, UserModel userInfo) {
        this.id = UUIDUtil.getUUID();
        this.skey = userInfo.getUcompany();
        this.infoId = oldInfo.getId();
        this.customerName = oldInfo.getCustomerName();
        this.belongType = BelongTypeEnum.客户.getCode();
        this.modifierId = userInfo.getId();
        this.modifierName = userInfo.getUname();
        this.oldAddressDetail = LambdaUtil.nullOfDefault(oldInfo.getCustomerArea(),area -> area.getDetail().getName(),"") + oldInfo.getDetailAddress();
        this.oldLongAndLat = oldInfo.getLongAndLatStr();
        this.modifyAddressDetail = LambdaUtil.nullOfDefault(customer.getCustomerArea(),area -> area.getDetail().getName(),"") + customer.getDetailAddress();
        this.modifyLongAndLat = customer.getLongAndLatStr();
    }
}
