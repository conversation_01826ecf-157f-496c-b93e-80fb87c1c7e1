<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.djcps.djeasyorder.addressupdaterecord.dao.AddressUpdateRecordDao">

    <resultMap id="BaseResultMap" type="com.djcps.djeasyorder.addressupdaterecord.model.po.CrmAddressUpdateRecordPo">
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="info_id" jdbcType="CHAR" property="infoId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="belong_type" jdbcType="TINYINT" property="belongType"/>
        <result column="modifier_id" jdbcType="CHAR" property="modifierId"/>
        <result column="modifier_name" jdbcType="VARCHAR" property="modifierName"/>
        <result column="old_address_detail" jdbcType="VARCHAR" property="oldAddressDetail"/>
        <result column="old_long_and_lat" jdbcType="VARCHAR" property="oldLongAndLat"/>
        <result column="modify_address_detail" jdbcType="VARCHAR" property="modifyAddressDetail"/>
        <result column="modify_long_and_lat" jdbcType="VARCHAR" property="modifyLongAndLat"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, create_time,info_id, customer_name,
        belong_type, modifier_id, modifier_name,
        old_address_detail, old_long_and_lat,
        modify_address_detail, modify_long_and_lat
    </sql>

    <insert id="insert">
        insert into crm_address_update_record (id, skey, 
          info_id, customer_name, belong_type, 
          modifier_id, modifier_name, old_address_detail, 
          old_long_and_lat, modify_address_detail, modify_long_and_lat
          )
        values (#{id}, #{skey},
          #{infoId}, #{customerName}, #{belongType}, 
          #{modifierId}, #{modifierName}, #{oldAddressDetail}, 
          #{oldLongAndLat}, #{modifyAddressDetail}, #{modifyLongAndLat}
          )
    </insert>

    <select id="getList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from crm_address_update_record
        where isdel = 0
        and skey = #{companyId}
        and create_time between #{startTime} and #{endTime}
        and modifier_id in
        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
</mapper>