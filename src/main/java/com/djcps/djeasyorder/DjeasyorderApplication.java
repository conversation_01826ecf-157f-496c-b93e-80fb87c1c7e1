package com.djcps.djeasyorder;

import com.alibaba.fastjson.parser.ParserConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.PropertySource;


/**
 * <AUTHOR>
 * @date 2021/4/23 9:10
 */
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication
@ComponentScan(basePackages = { "com.djcps.*" })
@EnableFeignClients(basePackages = "com.djcps.*")
@MapperScan(basePackages = {"com.djcps.djeasyorder.**.dao"})
@PropertySource(value = { "classpath:META-INF/app.properties" }, encoding = "UTF-8")
@EnableApolloConfig
public class DjeasyorderApplication {

    public static void main(String[] args) {
        System.setProperty("orgURL", "http://172.20.105.151:8080/djorg/");
        ParserConfig.getGlobalInstance().setSafeMode(true);
        SpringApplication.run(DjeasyorderApplication.class, args);
    }

}
