package com.djcps.djeasyorder.smartbrief.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.djcps.djeasyorder.common.utils.LambdaUtil;
import com.djcps.djeasyorder.common.utils.PageUtils;
import com.djcps.djeasyorder.common.utils.UserUtil;
import com.djcps.djeasyorder.smartbrief.dao.TaskResultDao;
import com.djcps.djeasyorder.smartbrief.dao.TaskResultItemDao;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo;
import com.djcps.djeasyorder.smartbrief.model.bo.UserTemplateBo;
import com.djcps.djeasyorder.smartbrief.model.vo.TaskResultVo;
import com.djcps.framework.httpformat.model.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.djcps.djeasyorder.common.utils.DateUtil.DATE_TIME_FORMATTER;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultServiceImpl {
    private final TaskResultDao taskResultDao;
    private final TaskResultItemDao taskResultItemDao;
    private final UserUtil userUtil;

    public PageResult<TaskResultVo> pageQuery(@Valid TaskResultSearchBo bo) {
        String role = bo.getRole();
        if ("salesman".equals(role)) {
            bo.setOrgId(null);
            bo.setUserId(userUtil.getUserId());
        } else if ("manager".equals(role)) {
            bo.setUserId(null);
            bo.setOrgId(bo.getOrgId());
        }
        PageResult<TaskResultPo> pageQuery = PageUtils.pageQuery(bo, () -> taskResultDao.pageQuery(bo));
        List<TaskResultPo> poList = pageQuery.getList();
        if (CollUtil.isEmpty(poList)) {
            return new PageResult<>();
        }
        List<Integer> collect = poList.stream().map(TaskResultPo::getId).collect(Collectors.toList());
        List<TaskResultItemPo> itemPoList = taskResultItemDao.getByTaskResultIds(collect);
        Map<Integer,   List<TaskResultItemPo>> itemPoMap = LambdaUtil.convertListMap(itemPoList, TaskResultItemPo::getResultId);
        List<TaskResultVo> list = new ArrayList<>();
        for (TaskResultPo resultPo : poList) {
            TaskResultVo e = new TaskResultVo(itemPoMap.get(resultPo.getId()));
            e.setDataDate(resultPo.getDataDate());
            e.setCreateTime(resultPo.getCreateTime().format(DATE_TIME_FORMATTER));
            list.add(e);
        }
        return new PageResult<>(pageQuery.getTotal(), list);
    }

    /**
     * 根据给定参数删除对应的task_result记录和对应的task_result_item记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeByTemplateDataDate(String skey, Long taskId, String dataDate, String cycleType, String supplierId) {
        // 先查询要删除的TaskResult的ID列表
        List<Integer> resultIds = taskResultDao.selectIdsByConditions(skey, taskId, dataDate, cycleType, supplierId);

        if (CollUtil.isNotEmpty(resultIds)) {
            // 删除关联的TaskResultItem记录
            taskResultItemDao.deleteByResultIds(resultIds);
        }

        // 删除TaskResult记录
        taskResultDao.deleteByConditions(skey, taskId, dataDate, cycleType, supplierId);
    }

    /**
     * 保存task_result_item记录
     */
    public Long saveResultItem(TaskResultPo taskResult, UserTemplateBo template, String batchNo, String originData) {
        TaskResultItemPo item = new TaskResultItemPo();
        item.setResultId(taskResult.getId());
        item.setTaskId(taskResult.getTaskId().intValue());
        item.setTemplateId(template.getId());
        item.setTemplateId(taskResult.gette);
        item.setOriginData(originData);
        item.setSkey(taskResult.getSkey());
        item.setPrompt(template.getPrompt());
        item.setSubBizType(template.getSubBizType());
        item.setBatchNo(batchNo);
        item.setCreateTime(LocalDateTime.now());
        item.setUpdateTime(LocalDateTime.now());

        taskResultItemDao.insertSelective(item);
        return item.getId().longValue();
    }

    /**
     * 更新task_result_item中指定id的item_result字段
     */
    public void updateResultItem(Long taskItemId, String analysisResult) {
        taskResultItemDao.updateItemResult(taskItemId, analysisResult);
    }
}
