package com.djcps.djeasyorder.smartbrief.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.djcps.djeasyorder.common.utils.LambdaUtil;
import com.djcps.djeasyorder.common.utils.PageUtils;
import com.djcps.djeasyorder.common.utils.UserUtil;
import com.djcps.djeasyorder.smartbrief.dao.TaskResultDao;
import com.djcps.djeasyorder.smartbrief.dao.TaskResultItemDao;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo;
import com.djcps.djeasyorder.smartbrief.model.vo.TaskResultVo;
import com.djcps.framework.httpformat.model.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.djcps.djeasyorder.common.utils.DateUtil.DATE_TIME_FORMATTER;

@Service
@Slf4j
@RequiredArgsConstructor
public class TaskResultServiceImpl {
    private final TaskResultDao taskResultDao;
    private final TaskResultItemDao taskResultItemDao;
    private final UserUtil userUtil;

    public PageResult<TaskResultVo> pageQuery(@Valid TaskResultSearchBo bo) {
        String role = bo.getRole();
        if ("salesman".equals(role)) {
            bo.setOrgId(null);
            bo.setUserId(userUtil.getUserId());
        } else if ("manager".equals(role)) {
            bo.setUserId(null);
            bo.setOrgId(bo.getOrgId());
        }
        PageResult<TaskResultPo> pageQuery = PageUtils.pageQuery(bo, () -> taskResultDao.pageQuery(bo));
        List<TaskResultPo> poList = pageQuery.getList();
        if (CollUtil.isEmpty(poList)) {
            return new PageResult<>();
        }
        List<Integer> collect = poList.stream().map(TaskResultPo::getId).collect(Collectors.toList());
        List<TaskResultItemPo> itemPoList = taskResultItemDao.getByTaskResultIds(collect);
        Map<Integer,   List<TaskResultItemPo>> itemPoMap = LambdaUtil.convertListMap(itemPoList, TaskResultItemPo::getResultId);
        List<TaskResultVo> list = new ArrayList<>();
        for (TaskResultPo resultPo : poList) {
            TaskResultVo e = new TaskResultVo(itemPoMap.get(resultPo.getId()));
            e.setDataDate(resultPo.getDataDate());
            e.setCreateTime(resultPo.getCreateTime().format(DATE_TIME_FORMATTER));
            list.add(e);
        }
        return new PageResult<>(pageQuery.getTotal(), list);
    }
}
