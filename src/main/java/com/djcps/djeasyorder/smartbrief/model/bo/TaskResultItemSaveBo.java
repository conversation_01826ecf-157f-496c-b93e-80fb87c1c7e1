package com.djcps.djeasyorder.smartbrief.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务结果子项保存参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultItemSaveBo {
    
    @NotNull(message = "resultId不能为空")
    private Integer resultId;
    
    @NotNull(message = "taskId不能为空")
    private Integer taskId;
    
    @NotNull(message = "templateId不能为空")
    private Integer templateId;
    
    @NotBlank(message = "originData不能为空")
    private String originData;
    
    @NotBlank(message = "skey不能为空")
    private String skey;
    
    @NotBlank(message = "prompt不能为空")
    private String prompt;
    
    @NotBlank(message = "subBizType不能为空")
    private String subBizType;
    
    @NotBlank(message = "batchNo不能为空")
    private String batchNo;
}
