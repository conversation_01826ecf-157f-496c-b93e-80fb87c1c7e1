package com.djcps.djeasyorder.smartbrief.model.po;

import java.time.LocalDateTime;

import lombok.Data;

/**
 * 任务结果
 *
 * <AUTHOR>
 * @create 2025-08-04 13:19
 */
@Data
public class TaskResultPo {
    private Integer id;

    private String skey;

    private String supplierId;

    /**
     * 名称
     */
    private Long taskId;

    /**
     * 角色: 业务员 salesman, 团长 group, 总经理 manager
     */
    private String role;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 组织id
     */
    private String orgId;

    private String batchNo;

    /**
     * 业务类型 交付:deliver
     */
    private String bizType;

    /**
     * 周期类型:day,week,month
     */
    private String cycleType;

    /**
     * 数据日期
     */
    private String dataDate;

    private LocalDateTime execStartTime;

    private LocalDateTime execEndTime;

    private LocalDateTime createTime;

    private Long isDel;
}