package com.djcps.djeasyorder.smartbrief.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 任务结果保存参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultSaveBo {
    
    @NotBlank(message = "skey不能为空")
    private String skey;
    
    @NotNull(message = "taskId不能为空")
    private Long taskId;
    
    @NotBlank(message = "dataDate不能为空")
    private String dataDate;
    
    @NotBlank(message = "bizType不能为空")
    private String bizType;
    
    @NotBlank(message = "cycleType不能为空")
    private String cycleType;
    
    private LocalDateTime execStartTime;
    
    @NotBlank(message = "batchNo不能为空")
    private String batchNo;
    
    private LocalDateTime execEndTime;
    
    private String userId;
    
    private String orgId;
    
    private String supplierId;
    
    /**
     * 角色: 业务员 salesman, 团长 group, 总经理 manager
     */
    private String role;
}
