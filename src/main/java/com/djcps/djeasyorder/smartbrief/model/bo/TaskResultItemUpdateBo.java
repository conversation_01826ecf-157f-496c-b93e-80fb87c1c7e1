package com.djcps.djeasyorder.smartbrief.model.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 任务结果子项更新参数
 *
 * <AUTHOR> Assistant
 * @create 2025-08-05
 */
@Data
public class TaskResultItemUpdateBo {
    
    @NotNull(message = "taskItemId不能为空")
    private Long taskItemId;
    
    @NotBlank(message = "analysisResult不能为空")
    private String analysisResult;
}
