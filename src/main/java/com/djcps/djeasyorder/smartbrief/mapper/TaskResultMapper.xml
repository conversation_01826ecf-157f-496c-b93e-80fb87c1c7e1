<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.djcps.djeasyorder.smartbrief.dao.TaskResultDao">
  <resultMap id="BaseResultMap" type="com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo">
    <!--@mbg.generated-->
    <!--@Table task_result-->
    <id column="id" property="id" />
    <result column="skey" property="skey" />
    <result column="supplier_id" property="supplierId" />
    <result column="task_id" property="taskId" />
    <result column="role" property="role" />
    <result column="user_id" property="userId" />
    <result column="org_id" property="orgId" />
    <result column="batch_no" property="batchNo" />
    <result column="biz_type" property="bizType" />
    <result column="cycle_type" property="cycleType" />
    <result column="data_date" property="dataDate" />
    <result column="exec_start_time" property="execStartTime" />
    <result column="exec_end_time" property="execEndTime" />
    <result column="create_time" property="createTime" />
    <result column="is_del" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, skey, supplier_id, task_id, `role`, user_id, org_id, batch_no, biz_type, cycle_type, 
    data_date, exec_start_time, exec_end_time, create_time, is_del
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from task_result
    where id = #{id}
  </select>
  <select id="pageQuery" parameterType="com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo" resultMap="BaseResultMap">

select   <include refid="Base_Column_List"/>
    from task_result where is_del=0
                            <if test="skey != null and skey != ''">
                            and skey = #{skey}
                            </if>
                            <if test="supplierId != null and supplierId != ''">
                            and supplier_id = #{supplierId}
                            </if>
                            <if test="orgId != null and orgId != ''">
                            and org_id = #{orgId}
                            </if>
                            <if test="userId != null and userId != ''">
                            and user_id = #{userId}
                            </if>
                            <if test="bizType != null and bizType != ''">
                            and biz_type = #{bizType}
                            </if>
                            <if test="cycleType != null and cycleType != ''">
                            and cycle_type = #{cycleType}
                            </if>
                            <if test="batchNo != null and batchNo != ''">
                            and batch_no = #{batchNo}
                            </if>
                            <if test="dataDate != null and dataDate != ''">
                            and data_date = #{dataDate}
                            </if>
order by data_date desc ,create_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from task_result
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into task_result (skey, supplier_id, task_id, `role`, user_id, org_id, batch_no, 
      biz_type, cycle_type, data_date, exec_start_time, exec_end_time, create_time, 
      is_del)
    values (#{skey}, #{supplierId}, #{taskId}, #{role}, #{userId}, #{orgId}, #{batchNo}, 
      #{bizType}, #{cycleType}, #{dataDate}, #{execStartTime}, #{execEndTime}, #{createTime}, 
      #{isDel})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into task_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skey != null">
        skey,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="role != null">
        `role`,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="cycleType != null">
        cycle_type,
      </if>
      <if test="dataDate != null">
        data_date,
      </if>
      <if test="execStartTime != null">
        exec_start_time,
      </if>
      <if test="execEndTime != null">
        exec_end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skey != null">
        #{skey},
      </if>
      <if test="supplierId != null">
        #{supplierId},
      </if>
      <if test="taskId != null">
        #{taskId},
      </if>
      <if test="role != null">
        #{role},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="batchNo != null">
        #{batchNo},
      </if>
      <if test="bizType != null">
        #{bizType},
      </if>
      <if test="cycleType != null">
        #{cycleType},
      </if>
      <if test="dataDate != null">
        #{dataDate},
      </if>
      <if test="execStartTime != null">
        #{execStartTime},
      </if>
      <if test="execEndTime != null">
        #{execEndTime},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="isDel != null">
        #{isDel},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo">
    <!--@mbg.generated-->
    update task_result
    <set>
      <if test="skey != null">
        skey = #{skey},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId},
      </if>
      <if test="taskId != null">
        task_id = #{taskId},
      </if>
      <if test="role != null">
        `role` = #{role},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType},
      </if>
      <if test="cycleType != null">
        cycle_type = #{cycleType},
      </if>
      <if test="dataDate != null">
        data_date = #{dataDate},
      </if>
      <if test="execStartTime != null">
        exec_start_time = #{execStartTime},
      </if>
      <if test="execEndTime != null">
        exec_end_time = #{execEndTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="isDel != null">
        is_del = #{isDel},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo">
    <!--@mbg.generated-->
    update task_result
    set skey = #{skey},
      supplier_id = #{supplierId},
      task_id = #{taskId},
      `role` = #{role},
      user_id = #{userId},
      org_id = #{orgId},
      batch_no = #{batchNo},
      biz_type = #{bizType},
      cycle_type = #{cycleType},
      data_date = #{dataDate},
      exec_start_time = #{execStartTime},
      exec_end_time = #{execEndTime},
      create_time = #{createTime},
      is_del = #{isDel}
    where id = #{id}
  </update>

  <!-- 根据条件删除任务结果 -->
  <update id="deleteByConditions">
    update task_result
    set is_del = 1
    where skey = #{skey}
      and task_id = #{taskId}
      and data_date = #{dataDate}
      and cycle_type = #{cycleType}
      and supplier_id = #{supplierId}
  </update>

  <!-- 根据条件查询任务结果ID列表 -->
  <select id="selectIdsByConditions" resultType="java.lang.Integer">
    select id
    from task_result
    where skey = #{skey}
      and task_id = #{taskId}
      and data_date = #{dataDate}
      and cycle_type = #{cycleType}
      and supplier_id = #{supplierId}
  </select>
</mapper>