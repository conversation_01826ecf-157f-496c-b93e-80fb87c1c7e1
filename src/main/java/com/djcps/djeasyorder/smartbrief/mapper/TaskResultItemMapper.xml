<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.djcps.djeasyorder.smartbrief.dao.TaskResultItemDao">
  <resultMap id="BaseResultMap" type="com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo">
    <!--@mbg.generated-->
    <!--@Table task_result_item-->
    <id column="id" property="id" />
    <result column="skey" property="skey" />
    <result column="task_id" property="taskId" />
    <result column="result_id" property="resultId" />
    <result column="sub_biz_type" property="subBizType" />
    <result column="batch_no" property="batchNo" />
    <result column="prompt" property="prompt" />
    <result column="origin_data" property="originData" />
    <result column="template_id" property="templateId" />
    <result column="item_result" property="itemResult" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="is_del" property="isDel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, skey, task_id, result_id, sub_biz_type, batch_no, prompt, origin_data, template_id, 
    item_result, create_time, update_time, is_del
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from task_result_item
    where id = #{id}
  </select>
  <select id="getByTaskResultIds"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_result_item
    where is_del = 0
    and result_id in
    <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    update task_result_item
    set is_del = 1
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into task_result_item (skey, task_id, result_id, sub_biz_type, batch_no, prompt, origin_data, 
      template_id, item_result, create_time, update_time, is_del)
    values (#{skey}, #{taskId}, #{resultId}, #{subBizType}, #{batchNo}, #{prompt}, #{originData}, 
      #{templateId}, #{itemResult}, #{createTime}, #{updateTime}, #{isDel})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into task_result_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skey != null">
        skey,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="resultId != null">
        result_id,
      </if>
      <if test="subBizType != null">
        sub_biz_type,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="prompt != null">
        prompt,
      </if>
      <if test="originData != null">
        origin_data,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="itemResult != null">
        item_result,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skey != null">
        #{skey},
      </if>
      <if test="taskId != null">
        #{taskId},
      </if>
      <if test="resultId != null">
        #{resultId},
      </if>
      <if test="subBizType != null">
        #{subBizType},
      </if>
      <if test="batchNo != null">
        #{batchNo},
      </if>
      <if test="prompt != null">
        #{prompt},
      </if>
      <if test="originData != null">
        #{originData},
      </if>
      <if test="templateId != null">
        #{templateId},
      </if>
      <if test="itemResult != null">
        #{itemResult},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="isDel != null">
        #{isDel},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo">
    <!--@mbg.generated-->
    update task_result_item
    <set>
      <if test="skey != null">
        skey = #{skey},
      </if>
      <if test="taskId != null">
        task_id = #{taskId},
      </if>
      <if test="resultId != null">
        result_id = #{resultId},
      </if>
      <if test="subBizType != null">
        sub_biz_type = #{subBizType},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo},
      </if>
      <if test="prompt != null">
        prompt = #{prompt},
      </if>
      <if test="originData != null">
        origin_data = #{originData},
      </if>
      <if test="templateId != null">
        template_id = #{templateId},
      </if>
      <if test="itemResult != null">
        item_result = #{itemResult},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="isDel != null">
        is_del = #{isDel},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo">
    <!--@mbg.generated-->
    update task_result_item
    set skey = #{skey},
      task_id = #{taskId},
      result_id = #{resultId},
      sub_biz_type = #{subBizType},
      batch_no = #{batchNo},
      prompt = #{prompt},
      origin_data = #{originData},
      template_id = #{templateId},
      item_result = #{itemResult},
      create_time = #{createTime},
      update_time = #{updateTime},
      is_del = #{isDel}
    where id = #{id}
  </update>

  <!-- 根据resultId列表删除任务结果子项 -->
  <update id="deleteByResultIds">
    update task_result_item
    set is_del = 1
    where result_id in
    <foreach collection="resultIds" item="resultId" index="index" open="(" close=")" separator=",">
      #{resultId}
    </foreach>
  </update>

  <!-- 更新指定id的itemResult字段 -->
  <update id="updateItemResult">
    update task_result_item
    set item_result = #{itemResult},
        update_time = now()
    where id = #{id}
  </update>
</mapper>