package com.djcps.djeasyorder.smartbrief.dao;

import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultDeleteBo;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo;
import com.djcps.djeasyorder.smartbrief.model.vo.TaskResultVo;
import org.apache.ibatis.annotations.Param;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-08-04 13:19
 */
public interface TaskResultDao {
    int deleteByPrimaryKey(Integer id);

    int insert(TaskResultPo record);

    int insertSelective(TaskResultPo record);

    TaskResultPo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TaskResultPo record);

    int updateByPrimaryKey(TaskResultPo record);

    List<TaskResultPo> pageQuery(@Valid TaskResultSearchBo bo);

    /**
     * 根据条件删除任务结果
     */
    int deleteByConditions(@Param("skey") String skey,
                          @Param("taskId") Long taskId,
                          @Param("dataDate") String dataDate,
                          @Param("cycleType") String cycleType,
                          @Param("supplierId") String supplierId);

    /**
     * 根据条件查询任务结果ID列表
     */
    List<Integer> selectIdsByConditions(@Param("skey") String skey,
                                       @Param("taskId") Long taskId,
                                       @Param("dataDate") String dataDate,
                                       @Param("cycleType") String cycleType,
                                       @Param("supplierId") String supplierId);
}