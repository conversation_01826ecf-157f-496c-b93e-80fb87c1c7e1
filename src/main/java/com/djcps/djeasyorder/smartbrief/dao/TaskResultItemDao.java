package com.djcps.djeasyorder.smartbrief.dao;

import com.djcps.djeasyorder.smartbrief.model.po.TaskResultItemPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-08-04 13:19
 */
public interface TaskResultItemDao {
    int deleteByPrimaryKey(Integer id);

    int insert(TaskResultItemPo record);

    int insertSelective(TaskResultItemPo record);

    TaskResultItemPo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TaskResultItemPo record);

    int updateByPrimaryKey(TaskResultItemPo record);

    List<TaskResultItemPo> getByTaskResultIds(@Param("ids") List<Integer> ids);

    /**
     * 根据resultId列表删除任务结果子项
     */
    int deleteByResultIds(@Param("resultIds") List<Integer> resultIds);

    /**
     * 更新指定id的itemResult字段
     */
    int updateItemResult(@Param("id") Long id, @Param("itemResult") String itemResult);
}