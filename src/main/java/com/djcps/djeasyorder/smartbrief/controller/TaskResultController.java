package com.djcps.djeasyorder.smartbrief.controller;

import com.djcps.djeasyorder.common.controller.BaseController;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultDeleteBo;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultItemSaveBo;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultItemUpdateBo;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSaveBo;
import com.djcps.djeasyorder.smartbrief.model.bo.TaskResultSearchBo;
import com.djcps.djeasyorder.smartbrief.model.po.TaskResultPo;
import com.djcps.djeasyorder.smartbrief.model.vo.TaskResultVo;
import com.djcps.djeasyorder.smartbrief.service.impl.TaskResultServiceImpl;
import com.djcps.framework.httpformat.model.HttpResult;
import com.djcps.framework.httpformat.model.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/result")
@RequiredArgsConstructor
public class TaskResultController  extends BaseController {
    private final TaskResultServiceImpl taskResultService;


    @PostMapping("page.do")
    public HttpResult<PageResult<TaskResultVo>> getListV2(@RequestBody @Valid TaskResultSearchBo bo) {
        bo.setUserId(userUtil.getUserId());
        taskResultService.pageQuery(bo);
        return toSuccessClient(taskResultService.pageQuery(bo));
    }

    /**
     * 根据给定参数删除对应的task_result记录和对应的task_result_item记录
     */
    @PostMapping("deleteCommon.do")
    public HttpResult<String> deleteByTemplateDataDate(@RequestBody @Valid TaskResultDeleteBo bo) {
        taskResultService.removeByTemplateDataDate(
            bo.getSkey(),
            bo.getTaskId(),
            bo.getDataDate(),
            bo.getCycleType(),
            bo.getSupplierId()
        );
        return toSuccessClient("删除成功");
    }

    /**
     * 保存task_result_item记录
     */
    @PostMapping("saveItemCommon.do")
    public HttpResult<Long> saveResultItem(@RequestBody @Valid TaskResultItemSaveBo bo) {
        Long itemId = taskResultService.saveResultItem(bo);
        return toSuccessClient(itemId);
    }

    /**
     * 更新task_result_item中指定id的item_result字段
     */
    @PostMapping("updateItemCommon.do")
    public HttpResult<String> updateResultItem(@RequestBody @Valid TaskResultItemUpdateBo bo) {
        taskResultService.updateResultItem(bo.getTaskItemId(), bo.getAnalysisResult());
        return toSuccessClient("更新成功");
    }

    /**
     * 保存任务结果
     */
    @PostMapping("saveCommon.do")
    public HttpResult<Integer> saveTaskResult(@RequestBody @Valid TaskResultSaveBo bo) {
        // 构建TaskResult对象
        TaskResultPo taskResult = new TaskResultPo();
        taskResult.setSkey(bo.getSkey());
        taskResult.setTaskId(bo.getTaskId());
        taskResult.setDataDate(bo.getDataDate());
        taskResult.setBizType(bo.getBizType());
        taskResult.setCycleType(bo.getCycleType());
        taskResult.setExecStartTime(bo.getExecStartTime());
        taskResult.setBatchNo(bo.getBatchNo());
        taskResult.setExecEndTime(bo.getExecEndTime());
        taskResult.setUserId(bo.getUserId());
        taskResult.setOrgId(bo.getOrgId());
        taskResult.setSupplierId(bo.getSupplierId());
        taskResult.setRole(bo.getRole());

        Integer taskResultId = taskResultService.saveTaskResult(taskResult);
        return toSuccessClient(taskResultId);
    }
}
