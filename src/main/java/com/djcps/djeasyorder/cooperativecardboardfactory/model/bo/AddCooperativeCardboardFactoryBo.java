package com.djcps.djeasyorder.cooperativecardboardfactory.model.bo;

import com.djcps.djeasyorder.customer.enums.BelongTypeEnum;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-10-31 9:32
 */
@Data
public class AddCooperativeCardboardFactoryBo {
    /**
     * 客户id
     */
    @NotBlank(message = "客户/线索id不能为空")
    private String infoId;

    /**
     * 所属类型
     */
    private BelongTypeEnum belongTypeEnum;

    /**
     * 合作纸板厂列表
     */
    @NotEmpty(message = "合作纸板厂不能为空")
    @Valid
    private Set<UpdateCooperativeCardboardFactoryBo> factoryList;
}
