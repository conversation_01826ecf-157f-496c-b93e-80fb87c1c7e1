package com.djcps.djeasyorder.cooperativecardboardfactory.model.bo;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.EvaluatePo;
import com.djcps.djeasyorder.customer.model.po.expand.Evaluate;
import com.djcps.djorg.commons.annotation.EnumValid;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-12 14:11
 */
@Data
public class EvaluateBo {
    /**
     * 质量
     */
    @EnumValid(target = Evaluate.Quality.class)
    private Integer quality;
    /**
     * 交期
     */
    @EnumValid(target = Evaluate.Delivery.class)
    private Integer delivery;
    /**
     * 价格
     */
    @EnumValid(target = Evaluate.Price.class)
    private Integer price;
    /**
     * 服务
     */
    @EnumValid(target = Evaluate.Service.class)
    private Integer service;
    /**
     * 账期
     */
    @EnumValid(target = Evaluate.AccountPeriod.class)
    private Integer accountPeriod;
    /**
     * 其他
     */
    @EnumValid(target = Evaluate.Other.class)
    private List<Integer> other;

    public EvaluateBo(EvaluatePo evaluate) {
        this.quality = evaluate.getEvaluateQuality();
        this.delivery = evaluate.getEvaluateDelivery();
        this.price = evaluate.getEvaluatePrice();
        this.service = evaluate.getEvaluateService();
        this.accountPeriod = evaluate.getEvaluateAccountPeriod();
        this.other = evaluate.getEvaluateOther();
    }

    public EvaluatePo toPo() {
        return new EvaluatePo(this);
    }
}
