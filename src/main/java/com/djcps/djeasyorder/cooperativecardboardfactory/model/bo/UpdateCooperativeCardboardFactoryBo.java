package com.djcps.djeasyorder.cooperativecardboardfactory.model.bo;

import com.djcps.djeasyorder.customer.enums.SettlementCycleEnum;
import com.djcps.djeasyorder.visit.record.model.po.CustomerFavouredPolicyModel;
import com.djcps.djorg.commons.annotation.EnumValid;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022-10-31 14:56
 */
@Data
public class UpdateCooperativeCardboardFactoryBo {
    /**
     * id
     */
    private String id;
    /**
     * 竞对公司id
     */
    private String rivalCompanyId;

    /**
     * 优惠政策
     */
    @Valid
    private CustomerFavouredPolicyModel favouredPolicy;

    /**
     * 结款周期(1-预付款,2-月结15天,3-30天,4-45天,5-60天,6-90天)
     *
     * @see SettlementCycleEnum
     */
    @EnumValid(target = SettlementCycleEnum.class)
    private Integer settlementCycle;

    /**
     * 付款方式(1-现金,2-电子承兑)
     */
    private List<Integer> payWay;

    /**
     * 备注说明
     */
    @Length(max = 100, message = "备注说明过长")
    private String remarks;

    /**
     * 评价
     */
    @Valid
    private EvaluateBo evaluate;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UpdateCooperativeCardboardFactoryBo that = (UpdateCooperativeCardboardFactoryBo) o;
        return Objects.equals(rivalCompanyId, that.rivalCompanyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(rivalCompanyId);
    }

}
