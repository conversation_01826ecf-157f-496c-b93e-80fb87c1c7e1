package com.djcps.djeasyorder.cooperativecardboardfactory.model.po;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.EvaluateBo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-15 13:58
 */
@Data
public class EvaluatePo {

    /**
     * 评价-质量(0-稳定,1-不稳定)
     */
    private Integer evaluateQuality;

    /**
     * 评价-交期(0-及时,1-不及时)
     */
    private Integer evaluateDelivery;

    /**
     * 评价-价格(0-便宜,1-贵)
     */
    private Integer evaluatePrice;

    /**
     * 评价-服务(0-售后及时,1-态度不好)
     */
    private Integer evaluateService;

    /**
     * 评价-账期(0-长,1-短)
     */
    private Integer evaluateAccountPeriod;

    /**
     * 评价-其他(0-小单服务及时,1-可做特殊材料)
     */
    private List<Integer> evaluateOther;

    public EvaluatePo() {
    }

    public EvaluatePo(EvaluateBo model) {
        if (model == null) {
            return;
        }
        this.evaluateQuality = model.getQuality();
        this.evaluateDelivery = model.getDelivery();
        this.evaluatePrice = model.getPrice();
        this.evaluateService = model.getService();
        this.evaluateAccountPeriod = model.getAccountPeriod();
        this.evaluateOther = model.getOther();
    }
}
