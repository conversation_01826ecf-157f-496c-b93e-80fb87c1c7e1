package com.djcps.djeasyorder.cooperativecardboardfactory.model.po;

import cn.hutool.core.collection.CollUtil;
import com.djcps.djeasyorder.fieldconfig.aop.annotation.FieldHideMarkAnnotation;
import com.djcps.djeasyorder.fieldconfig.constant.MarkConstant;
import com.djcps.djeasyorder.fieldconfig.model.HideFieldUpdateModel;
import com.djcps.djeasyorder.visit.record.model.po.FavouredPolicyHideFieldModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 13:23
 */
@Data
public class CooperativeCardboardFactoryHideFieldPo {

    /**
     * 唯一id
     */
    private String id;

    /**
     * 合作纸板厂id
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_RIVAL_COMPANY)
    private HideFieldUpdateModel<String> rivalCompanyId;

    /**
     * 优惠政策
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_FAVOURED_POLICY)
    private HideFieldUpdateModel<FavouredPolicyHideFieldModel> favouredPolicy;

    /**
     * 结款周期
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_SETTLEMENT_CYCLE)
    private HideFieldUpdateModel<Integer> settlementCycle;

    /**
     * 付款方式(1-现金,2-电子承兑)
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_PAY_WAY)
    private HideFieldUpdateModel<String> payWay;

    /**
     * 备注说明
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_REMARKS)
    private HideFieldUpdateModel<String> remarks;

    /**
     * 评价
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_EVALUATE)
    private HideFieldUpdateModel<EvaluatePo> evaluate;

    public HideFieldUpdateModel<EvaluatePo> getEvaluate() {
        if (this.evaluate == null) {
            return new HideFieldUpdateModel<>(new EvaluatePo());
        }
        return evaluate;
    }

    public CooperativeCardboardFactoryHideFieldPo(CooperativeCardboardFactoryPo model) {
        this.id = model.getId();
        this.rivalCompanyId = new HideFieldUpdateModel<>(model.getRivalCompanyId());
        this.favouredPolicy = new HideFieldUpdateModel<>(new FavouredPolicyHideFieldModel(model.getFavouredPolicy()));
        this.settlementCycle = new HideFieldUpdateModel<>(model.getSettlementCycle());
        List<Integer> payWay1 = model.getPayWay();
        if (CollUtil.isNotEmpty(payWay1)) {
            this.payWay = new HideFieldUpdateModel<>(CollUtil.join(payWay1,","));
        }else {
            this.payWay = new HideFieldUpdateModel<>(null);
        }
        this.remarks = new HideFieldUpdateModel<>(model.getRemarks());
        this.evaluate = new HideFieldUpdateModel<>( model.getEvaluate());
    }
}