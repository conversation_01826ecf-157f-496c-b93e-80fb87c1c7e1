package com.djcps.djeasyorder.cooperativecardboardfactory.model.po;

import com.djcps.djeasyorder.common.utils.UUIDUtil;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.AddCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.UpdateCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.visit.record.model.po.CustomerFavouredPolicyModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 13:23
 */
@Data
public class CooperativeCardboardFactoryPo {

    /**
     * 唯一id
     */
    private String id;

    /**
     * 拆分键
     */
    private String skey;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 合作纸板厂id
     */
    private String rivalCompanyId;

    /**
     * 客户/线索id
     */
    private String infoId;

    /**
     * 所属类型(0-客户,1-线索)
     */
    private Integer belongType;

    /**
     * 优惠政策
     */
    private CustomerFavouredPolicyModel favouredPolicy;

    /**
     * 结款周期
     */
    private Integer settlementCycle;

    /**
     * 付款方式(1-现金,2-电子承兑)
     */
    private List<Integer> payWay;

    /**
     * 备注说明
     */
    private String remarks;

    /**
     * 评价
     */
    private EvaluatePo evaluate;

    public EvaluatePo getEvaluate() {
        if (this.evaluate == null) {
            return new EvaluatePo();
        }
        return evaluate;
    }

    public CooperativeCardboardFactoryPo() {
    }

    public CooperativeCardboardFactoryPo(UpdateCooperativeCardboardFactoryBo model) {
        this.id = model.getId();
        this.favouredPolicy = model.getFavouredPolicy();
        if (this.favouredPolicy != null) {
            this.favouredPolicy.syncUpdateTimeAsNow();
        }
        this.settlementCycle = model.getSettlementCycle();
        this.payWay = model.getPayWay();
        this.remarks = model.getRemarks();
        this.evaluate = new EvaluatePo(model.getEvaluate());
    }

    public CooperativeCardboardFactoryPo(UpdateCooperativeCardboardFactoryBo model, AddCooperativeCardboardFactoryBo bo, String skey) {
        this.id = UUIDUtil.getUUID();
        this.skey = skey;
        this.infoId = bo.getInfoId();
        this.belongType = bo.getBelongTypeEnum().getCode();
        this.favouredPolicy = model.getFavouredPolicy();
        if (this.favouredPolicy != null) {
            this.favouredPolicy.syncUpdateTimeAsNow();
        }
        this.settlementCycle = model.getSettlementCycle();
        this.payWay = model.getPayWay();
        this.remarks = model.getRemarks();
        if (model.getEvaluate() != null) {
            this.evaluate = model.getEvaluate().toPo();
        }
    }

    public CooperativeCardboardFactoryPo(CooperativeCardboardFactoryPo model, String skey) {
        this.id = UUIDUtil.getUUID();
        this.skey = skey;
        this.favouredPolicy = model.getFavouredPolicy();
        if (this.favouredPolicy != null) {
            this.favouredPolicy.syncUpdateTimeAsNow();
        }
        this.settlementCycle = model.getSettlementCycle();
        this.payWay = model.getPayWay();
        this.remarks = model.getRemarks();
        this.rivalCompanyId = model.getRivalCompanyId();
        this.evaluate = model.getEvaluate();
    }
}