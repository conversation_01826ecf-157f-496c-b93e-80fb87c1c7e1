package com.djcps.djeasyorder.cooperativecardboardfactory.model.vo;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.EvaluateBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.EvaluatePo;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-05-17 13:30
 */
@Data
public class EvaluateVo {

    /**
     * 评价-质量(0-稳定,1-不稳定)
     */
    private Integer quality;

    /**
     * 评价-交期(0-及时,1-不及时)
     */
    private Integer delivery;

    /**
     * 评价-价格(0-便宜,1-贵)
     */
    private Integer price;

    /**
     * 评价-服务(0-售后及时,1-态度不好)
     */
    private Integer service;

    /**
     * 评价-账期(0-长,1-短)
     */
    private Integer accountPeriod;

    /**
     * 评价-其他(0-小单服务及时,1-可做特殊材料)
     */
    private List<Integer> other;

    public EvaluateVo() {
    }

    public EvaluateVo(EvaluatePo model) {
        this.quality = model.getEvaluateQuality();
        this.delivery = model.getEvaluateDelivery();
        this.price = model.getEvaluatePrice();
        this.service = model.getEvaluateService();
        this.accountPeriod = model.getEvaluateAccountPeriod();
        this.other = model.getEvaluateOther();
    }

    public EvaluateVo(EvaluateBo model) {
        this.quality = model.getQuality();
        this.delivery = model.getDelivery();
        this.price = model.getPrice();
        this.service = model.getService();
        this.accountPeriod = model.getAccountPeriod();
        this.other = model.getOther();
    }

    public boolean isEmpty() {
        return ObjectUtils.isEmpty(this.quality) &&
                ObjectUtils.isEmpty(this.delivery) &&
                ObjectUtils.isEmpty(this.price) &&
                ObjectUtils.isEmpty(this.service) &&
                ObjectUtils.isEmpty(this.accountPeriod) &&
                ObjectUtils.isEmpty(this.other);
    }
}
