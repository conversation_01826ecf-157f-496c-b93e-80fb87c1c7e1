package com.djcps.djeasyorder.cooperativecardboardfactory.model.vo;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryPo;
import com.djcps.djeasyorder.fieldconfig.aop.annotation.FieldHideMarkAnnotation;
import com.djcps.djeasyorder.fieldconfig.constant.MarkConstant;
import com.djcps.djeasyorder.visit.record.model.po.FavouredPolicyModel;
import lombok.Data;

import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-31 10:49
 */
@Data
public class CooperativeCardboardFactoryVo {
    /**
     * 唯一id
     */
    private String id;

    /**
     * 合作纸板厂id
     */
    private String rivalCompanyId;

    /**
     * 竞争程度排序code(3:strong,2:medium,1:weak)
     */
    private Integer degreeOfCompetitionSortCode;

    /**
     * 合作纸板厂名称
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_RIVAL_COMPANY)
    private String rivalCompanyName;

    /**
     * 优惠政策
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_FAVOURED_POLICY)
    private FavouredPolicyModel favouredPolicy;

    /**
     * 结款周期
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_SETTLEMENT_CYCLE)
    private Integer settlementCycle;

    /**
     * 结算方式(1-毛料结算,2-净料结算)
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_SETTLE_METHOD)
    private Integer settleMethod;

    /**
     * 付款方式(1-现金,2-电子承兑)
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_PAY_WAY)
    private List<Integer> payWay;

    /**
     * 备注说明
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_REMARKS)
    private String remarks;

    /**
     * 评价
     */
    @FieldHideMarkAnnotation(mark = MarkConstant.CustomerClue.FACTORY_EVALUATE)
    private EvaluateVo evaluate;

    public CooperativeCardboardFactoryVo() {

    }

    public CooperativeCardboardFactoryVo(CooperativeCardboardFactoryPo model) {
        this.id = model.getId();
        this.rivalCompanyId = model.getRivalCompanyId();
        this.favouredPolicy = model.getFavouredPolicy();
        this.settlementCycle = model.getSettlementCycle();
        this.payWay = model.getPayWay();
        this.remarks = model.getRemarks();
        this.evaluate = new EvaluateVo(model.getEvaluate());
    }

    /**
     * 获取排序比较器
     */
    public static void sortByCompetitionDegree(List<CooperativeCardboardFactoryVo> list) {
        if (list == null) {
            return;
        }

        list.sort(new Comparator<CooperativeCardboardFactoryVo>() {
            @Override
            public int compare(CooperativeCardboardFactoryVo o1, CooperativeCardboardFactoryVo o2) {
                // 处理null值情况，将degreeOfCompetitionSortCode为null的排在后面
                if (o1.getDegreeOfCompetitionSortCode() == null && o2.getDegreeOfCompetitionSortCode() == null) {
                    return compareById(o1, o2);
                }
                if (o1.getDegreeOfCompetitionSortCode() == null) {
                    return 1; // o1排在后面
                }
                if (o2.getDegreeOfCompetitionSortCode() == null) {
                    return -1; // o2排在后面
                }

                // 比较竞争强度（从大到小排序）
                int competitionCompare = o2.getDegreeOfCompetitionSortCode().compareTo(o1.getDegreeOfCompetitionSortCode());
                if (competitionCompare != 0) {
                    return competitionCompare;
                }
                // 竞争强度相同，按照id排序
                return compareById(o1, o2);
            }

            private int compareById(CooperativeCardboardFactoryVo o1, CooperativeCardboardFactoryVo o2) {
                return o1.getRivalCompanyName().compareTo(o2.getRivalCompanyName());
            }
        });
    }
}
