package com.djcps.djeasyorder.cooperativecardboardfactory.model.po;

import lombok.Data;

import java.util.List;

/**
 * 合作纸板厂查询条件模型
 *
 * <AUTHOR>
 * @create 2022-11-03 17:02
 */
@Data
public class CooperativeCardboardFactoryCondition {
    /**
     * 客户/线索id
     */
    private String infoId;
    /**
     * 合作纸板厂id
     */
    private String rivalCompanyId;
    /**
     * 合作纸板厂id集合
     */
    private List<String> rivalCompanyIdList;
    /**
     * 所属类型(0-客户,1-线索)
     */
    private Integer belongType;
}
