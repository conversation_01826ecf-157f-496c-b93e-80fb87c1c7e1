package com.djcps.djeasyorder.cooperativecardboardfactory.service.impl;

import com.djcps.djbase.commons.exception.DJException;
import com.djcps.djeasyorder.clue.enums.ClueOperateTypeEnum;
import com.djcps.djeasyorder.clue.service.ClueBaseService;
import com.djcps.djeasyorder.common.msg.CurrencyMsg;
import com.djcps.djeasyorder.common.service.BaseService;
import com.djcps.djeasyorder.common.utils.EnumUtil;
import com.djcps.djeasyorder.common.utils.LambdaUtil;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryCondition;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryHideFieldPo;
import com.djcps.djeasyorder.cooperativecardboardfactory.service.CooperativeCardboardFactoryService;
import com.djcps.djeasyorder.customer.service.CustomerBaseService;
import com.djcps.djeasyorder.cooperativecardboardfactory.msg.CooperativeCardboardFactoryMsg;
import com.djcps.djeasyorder.cooperativecardboardfactory.dao.CooperativeCardboardFactoryDao;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.UpdateCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryPo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.AddCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.vo.CooperativeCardboardFactoryVo;
import com.djcps.djeasyorder.customer.enums.BelongTypeEnum;
import com.djcps.djeasyorder.operationlog.OperationLogService;
import com.djcps.djeasyorder.rival.enums.DegreeOfCompetitionEnum;
import com.djcps.djeasyorder.rival.model.po.RivalArchivePo;
import com.djcps.djeasyorder.rival.service.RivalArchiveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2022-10-31 9:25
 */
@Service
@Slf4j
public class CooperativeCardboardFactoryServiceImpl extends BaseService implements CooperativeCardboardFactoryService {

    @Autowired
    private CooperativeCardboardFactoryDao cooperativeCardboardFactoryDao;

    @Autowired
    private CustomerBaseService customerBaseService;

    @Autowired
    private ClueBaseService clueBaseService;

    @Autowired
    private RivalArchiveService rivalArchiveService;

    @Autowired
    private OperationLogService operationLogService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCooperativeCardboardFactory(AddCooperativeCardboardFactoryBo bo) {
        // 去重校验
        addCooperativeCardboardFactoryCheck(bo);
        List<CooperativeCardboardFactoryPo> insertList = getInsertList(bo);
        // 循环插入
        insertList.forEach(cooperativeCardboardFactoryDao::insert);
        // 修改客户/线索信息完整度
        updateBaseInfoPercent(bo.getBelongTypeEnum(), bo.getInfoId());
        // 添加操作日志
        operationLogService.addOperateInfo(bo.getBelongTypeEnum(), ClueOperateTypeEnum.UPDATE, bo.getInfoId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCooperativeCardboardFactoryWithLog(AddCooperativeCardboardFactoryBo bo) {
        addCooperativeCardboardFactory(bo);
    }

    @Override
    public void addCooperativeCardboardFactoryInfo(CooperativeCardboardFactoryPo param) {
        cooperativeCardboardFactoryDao.insert(param);
    }

    /**
     * 修改客户/线索信息完整度
     */
    private void updateBaseInfoPercent(BelongTypeEnum belongTypeEnum, String infoId) {
        if (BelongTypeEnum.客户.equals(belongTypeEnum)) {
            customerBaseService.calculateAndSetInfoPercent(infoId);
        } else if (BelongTypeEnum.线索.equals(belongTypeEnum)) {
            clueBaseService.calculateAndSetInfoPercent(infoId);
        }
    }

    private List<CooperativeCardboardFactoryPo> getInsertList(AddCooperativeCardboardFactoryBo bo) {
        Set<UpdateCooperativeCardboardFactoryBo> factoryList = bo.getFactoryList();
        // 获取合作纸板厂id集合
        List<String> rivalCompanyIdList = LambdaUtil.convertList(factoryList, UpdateCooperativeCardboardFactoryBo::getRivalCompanyId);
        List<RivalArchivePo> rivalArchivePoList = rivalArchiveService.getPoByIdList(rivalCompanyIdList, null);
        Map<String, RivalArchivePo> rivalArchivePoMap = LambdaUtil.convertMap(rivalArchivePoList, RivalArchivePo::getId);
        // 获取合作纸板厂信息
        return factoryList.stream()
                .map(x -> {
                    CooperativeCardboardFactoryPo model = new CooperativeCardboardFactoryPo(x, bo, userUtil.getUserInfo().getUcompany());
                    String rivalCompanyId = x.getRivalCompanyId();
                    RivalArchivePo rivalArchivePo = rivalArchivePoMap.get(rivalCompanyId);
                    if (ObjectUtils.isEmpty(rivalArchivePo)) {
                        throw new DJException(CooperativeCardboardFactoryMsg.无法获取合作纸板厂信息);
                    }
                    model.setRivalCompanyId(rivalCompanyId);
                    return model;
                }).collect(Collectors.toList());
    }

    private void addCooperativeCardboardFactoryCheck(AddCooperativeCardboardFactoryBo bo) {
        // 先获取已存在的
        List<CooperativeCardboardFactoryPo> factoryPoList = getPoByInfoId(bo.getInfoId());
        if (ObjectUtils.isEmpty(factoryPoList)) {
            return;
        }
        Set<UpdateCooperativeCardboardFactoryBo> factoryList = bo.getFactoryList();
        // 获取合作纸板厂id集合
        List<String> rivalCompanyIdList = LambdaUtil.convertList(factoryList, UpdateCooperativeCardboardFactoryBo::getRivalCompanyId);
        factoryPoList.forEach(x -> {
            if (rivalCompanyIdList.contains(x.getRivalCompanyId())) {
                throw new DJException(CooperativeCardboardFactoryMsg.合作纸板厂重复添加);
            }
        });
    }

    @Override
    public List<CooperativeCardboardFactoryVo> getVoByInfoId(String infoId) {
        List<CooperativeCardboardFactoryPo> listPo = getPoByInfoId(infoId);
        List<String> rivalCompanyIdList = LambdaUtil.convertList(listPo, CooperativeCardboardFactoryPo::getRivalCompanyId);
        List<RivalArchivePo> rivalCompanyList = rivalArchiveService.getPoByIdList(rivalCompanyIdList, null);
        Map<String, RivalArchivePo> rivalCompanyMap = LambdaUtil.convertMap(rivalCompanyList, RivalArchivePo::getId);
        List<CooperativeCardboardFactoryVo> list = listPo.stream().map(x -> {
            CooperativeCardboardFactoryVo vo = new CooperativeCardboardFactoryVo(x);
            String rivalCompanyId = vo.getRivalCompanyId();
            RivalArchivePo rivalCompany = rivalCompanyMap.get(rivalCompanyId);
            if (!ObjectUtils.isEmpty(rivalCompany)) {
                vo.setRivalCompanyName(rivalCompany.getCompanyName());
                vo.setSettleMethod(rivalCompany.getSettleMethod());
                String degreeOfCompetition = rivalCompany.getDegreeOfCompetition();
                // 获取竞争强度枚举
                DegreeOfCompetitionEnum competitionEnum = EnumUtil.valueOfStringCode(DegreeOfCompetitionEnum.class, degreeOfCompetition);
                if (competitionEnum != null) {
                    vo.setDegreeOfCompetitionSortCode(competitionEnum.getSort());
                }
            }
            return vo;
        }).collect(Collectors.toList());
        // 排序
        CooperativeCardboardFactoryVo.sortByCompetitionDegree(list);
        return list;
    }

    @Override
    public List<CooperativeCardboardFactoryPo> getPoByInfoId(String infoId) {
        return cooperativeCardboardFactoryDao.getListByInfoId(infoId);
    }

    @Override
    public List<CooperativeCardboardFactoryPo> getListByInfoIdList(List<String> infoId) {
        return cooperativeCardboardFactoryDao.getListByInfoIdList(infoId);
    }

    @Override
    public List<CooperativeCardboardFactoryPo> getPoByCondition(CooperativeCardboardFactoryCondition condition) {
        return cooperativeCardboardFactoryDao.getPoByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCooperativeCardboardFactory(UpdateCooperativeCardboardFactoryBo bo) {
        CooperativeCardboardFactoryPo update = new CooperativeCardboardFactoryPo(bo);
        update.setRivalCompanyId(bo.getRivalCompanyId());
        CooperativeCardboardFactoryHideFieldPo hideFieldPo = new CooperativeCardboardFactoryHideFieldPo(update);
        cooperativeCardboardFactoryDao.updateHideField(hideFieldPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CooperativeCardboardFactoryPo updateCooperativeCardboardFactoryWithLog(UpdateCooperativeCardboardFactoryBo bo) {
        updateCooperativeCardboardFactory(bo);
        CooperativeCardboardFactoryPo factoryInfo = cooperativeCardboardFactoryDao.getInfoById(bo.getId());
        if (ObjectUtils.isEmpty(factoryInfo)) {
            throw new DJException(CooperativeCardboardFactoryMsg.无法获取合作纸板厂信息);
        }
        // 添加操作日志
        operationLogService.addOperateInfo(EnumUtil.valueOfCode(BelongTypeEnum.class, factoryInfo.getBelongType()), ClueOperateTypeEnum.UPDATE, factoryInfo.getInfoId());
        return factoryInfo;
    }

    @Override
    public void updateNotNull(CooperativeCardboardFactoryPo model) {
        cooperativeCardboardFactoryDao.updateNotNull(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCooperativeCardboardFactory(String id) {
        CooperativeCardboardFactoryPo infoById = cooperativeCardboardFactoryDao.getInfoById(id);
        if (ObjectUtils.isEmpty(infoById)) {
            throw new DJException(CurrencyMsg.OPERATION_FAILURE);
        }
        cooperativeCardboardFactoryDao.del(id);
        // 修改客户信息完整度
        updateBaseInfoPercent(EnumUtil.valueOfCode(BelongTypeEnum.class, infoById.getBelongType()), infoById.getInfoId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CooperativeCardboardFactoryPo delCooperativeCardboardFactoryWithLog(String id) {
        CooperativeCardboardFactoryPo factoryInfo = cooperativeCardboardFactoryDao.getInfoById(id);
        if (ObjectUtils.isEmpty(factoryInfo)) {
            throw new DJException(CooperativeCardboardFactoryMsg.无法获取合作纸板厂信息);
        }
        delCooperativeCardboardFactory(id);
        // 添加操作日志
        operationLogService.addOperateInfo(EnumUtil.valueOfCode(BelongTypeEnum.class, factoryInfo.getBelongType()), ClueOperateTypeEnum.UPDATE, factoryInfo.getInfoId());
        return factoryInfo;
    }

    @Override
    public void updateForList(String infoId, BelongTypeEnum typeEnum, Set<UpdateCooperativeCardboardFactoryBo> factoryBoSet) {
        if (factoryBoSet == null) {
            return;
        }
        // 已存在的
        List<CooperativeCardboardFactoryPo> existList = getPoByInfoId(infoId);
        if (ObjectUtils.isEmpty(existList) && ObjectUtils.isEmpty(factoryBoSet)) {
            return;
        }
        List<String> existIdList = LambdaUtil.convertList(existList, CooperativeCardboardFactoryPo::getId);
        Set<UpdateCooperativeCardboardFactoryBo> insetSet = new HashSet<>();
        Set<UpdateCooperativeCardboardFactoryBo> updateSet = new HashSet<>();
        // 新增
        for (UpdateCooperativeCardboardFactoryBo factoryBo : factoryBoSet) {
            if (ObjectUtils.isEmpty(factoryBo.getId())) {
                insetSet.add(factoryBo);
            } else {
                updateSet.add(factoryBo);
            }
        }
        List<String> updateIdList = LambdaUtil.convertList(updateSet, UpdateCooperativeCardboardFactoryBo::getId);
        // 已存在的，去除掉所有修改的，剩下的为删除
        existIdList.removeAll(updateIdList);
        // 删除
        existIdList.forEach(this::delCooperativeCardboardFactory);
        // 新增
        AddCooperativeCardboardFactoryBo addModel = new AddCooperativeCardboardFactoryBo();
        addModel.setInfoId(infoId);
        addModel.setBelongTypeEnum(typeEnum);
        addModel.setFactoryList(insetSet);
        addCooperativeCardboardFactory(addModel);
        // 修改
        updateSet.forEach(this::updateCooperativeCardboardFactory);
    }

}