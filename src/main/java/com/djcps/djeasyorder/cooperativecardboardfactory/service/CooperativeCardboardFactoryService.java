package com.djcps.djeasyorder.cooperativecardboardfactory.service;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.AddCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.bo.UpdateCooperativeCardboardFactoryBo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryCondition;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryPo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.vo.CooperativeCardboardFactoryVo;
import com.djcps.djeasyorder.customer.enums.BelongTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2022-10-31 9:25
 */
public interface CooperativeCardboardFactoryService {

    /**
     * 新增客户/线索的合作纸板厂
     *
     * @param bo 新增模型
     */
    void addCooperativeCardboardFactory(AddCooperativeCardboardFactoryBo bo);

    /**
     * 新增客户/线索的合作纸板厂(携带操作日志)
     *
     * @param bo 新增模型
     */
    void addCooperativeCardboardFactoryWithLog(AddCooperativeCardboardFactoryBo bo);

    /**
     * 新增客户/线索的合作纸板厂
     *
     * @param bo 新增模型
     */
    void addCooperativeCardboardFactoryInfo(CooperativeCardboardFactoryPo bo);

    /**
     * 通过客户/线索id获取
     *
     * @param infoId 客户/线索id
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryVo> getVoByInfoId(String infoId);

    /**
     * 通过客户/线索id获取
     *
     * @param infoId 客户/线索id
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getPoByInfoId(String infoId);

    /**
     * 通过客户/线索id集合获取
     *
     * @param infoId 客户/线索id集合
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getListByInfoIdList(List<String> infoId);

    /**
     * 按条件查询
     *
     * @param condition 查询条件
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getPoByCondition(CooperativeCardboardFactoryCondition condition);

    /**
     * 修改客户/线索的合作纸板厂
     *
     * @param bo 修改模型
     */
    void updateCooperativeCardboardFactory(UpdateCooperativeCardboardFactoryBo bo);

    /**
     * 修改客户/线索的合作纸板厂(携带操作日志)
     *
     * @param bo 修改模型
     */
    CooperativeCardboardFactoryPo updateCooperativeCardboardFactoryWithLog(UpdateCooperativeCardboardFactoryBo bo);

    /**
     * 修改非空
     *
     * @param model 修改模型
     */
    void updateNotNull(CooperativeCardboardFactoryPo model);

    /**
     * 删除客户/线索的合作纸板厂
     *
     * @param id id
     */
    void delCooperativeCardboardFactory(String id);

    /**
     * 删除客户/线索的合作纸板厂
     */
    CooperativeCardboardFactoryPo delCooperativeCardboardFactoryWithLog(String id);

    /**
     * 通过集合修改
     *
     * @param infoId    客户/线索id
     * @param typeEnum  类型
     * @param factoryBo 修改模型
     */
    void updateForList(String infoId, BelongTypeEnum typeEnum, Set<UpdateCooperativeCardboardFactoryBo> factoryBo);
}