package com.djcps.djeasyorder.cooperativecardboardfactory.dao;

import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryCondition;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryHideFieldPo;
import com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryPo;
import com.djcps.djeasyorder.fieldconfig.aop.annotation.FieldHideUpdateDaoHandler;
import com.djcps.djeasyorder.fieldconfig.enums.BelongModuleEnum;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-10-31 10:55
 */
@Repository
public interface CooperativeCardboardFactoryDao {
    /**
     * 通过id获取合作纸板厂信息
     *
     * @param id id
     * @return 合作工厂
     */
    CooperativeCardboardFactoryPo getInfoById(@Param("id") String id);

    /**
     * 新增客户/线索的合作工厂
     *
     * @param model 新增模型
     */
    void insert(CooperativeCardboardFactoryPo model);

    /**
     * 通过客户/线索id获取
     *
     * @param infoId 客户/线索id
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getListByInfoId(@Param("infoId") String infoId);

    /**
     * 通过客户/线索id获取
     *
     * @param infoIdList 客户/线索id集合
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getListByInfoIdList(@Param("infoIdList") List<String> infoIdList);

    /**
     * 按条件查询
     *
     * @param condition 查询条件
     * @return 合作工厂列表
     */
    List<CooperativeCardboardFactoryPo> getPoByCondition(CooperativeCardboardFactoryCondition condition);

    /**
     * 修改非空
     *
     * @param model 修改模型
     */
    void updateNotNull(CooperativeCardboardFactoryPo model);

    /**
     * 修改
     *
     * @param model 修改模型
     */
    void update(CooperativeCardboardFactoryPo model);

    /**
     * 修改（隐藏字段逻辑处理）
     *
     * @param model 修改模型
     */
    @FieldHideUpdateDaoHandler(module = BelongModuleEnum.客户线索)
    void updateHideField(CooperativeCardboardFactoryHideFieldPo model);

    /**
     * id
     *
     * @param id id
     */
    void del(@Param("id") String id);
}