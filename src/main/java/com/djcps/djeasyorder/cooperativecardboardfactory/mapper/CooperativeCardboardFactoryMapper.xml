<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.djcps.djeasyorder.cooperativecardboardfactory.dao.CooperativeCardboardFactoryDao">

    <resultMap id="BaseResultMap"
               type="com.djcps.djeasyorder.cooperativecardboardfactory.model.po.CooperativeCardboardFactoryPo">
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="skey" jdbcType="CHAR" property="skey"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="rival_company_id" jdbcType="CHAR" property="rivalCompanyId"/>
        <result column="info_id" jdbcType="CHAR" property="infoId"/>
        <result column="belong_type" jdbcType="TINYINT" property="belongType"/>
        <result column="favoured_policy" jdbcType="VARCHAR" property="favouredPolicy"
                typeHandler="com.djcps.djeasyorder.common.typehandler.CustomerFavouredPolicyTypeHandler"/>
        <result column="settlement_cycle" jdbcType="TINYINT" property="settlementCycle"/>
        <result column="pay_way" jdbcType="TINYINT" property="payWay" typeHandler="com.djcps.djeasyorder.common.typehandler.StringToListTypeHandler"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <association property="evaluate" javaType="com.djcps.djeasyorder.cooperativecardboardfactory.model.po.EvaluatePo">
            <result column="evaluate_quality" jdbcType="TINYINT" property="evaluateQuality" />
            <result column="evaluate_delivery" jdbcType="TINYINT" property="evaluateDelivery" />
            <result column="evaluate_price" jdbcType="TINYINT" property="evaluatePrice" />
            <result column="evaluate_service" jdbcType="TINYINT" property="evaluateService" />
            <result column="evaluate_account_period" jdbcType="TINYINT" property="evaluateAccountPeriod" />
            <result column="evaluate_other" jdbcType="VARCHAR" property="evaluateOther" typeHandler="com.djcps.djeasyorder.common.typehandler.IntegerListTypeHandler"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
    id, skey, create_time, update_time, rival_company_id,
    info_id, belong_type, favoured_policy,
    settlement_cycle, pay_way, remarks,
    evaluate_quality, evaluate_delivery, evaluate_price,
    evaluate_service, evaluate_account_period, evaluate_other
  </sql>

    <insert id="insert">
        INSERT INTO crm_cooperative_cardboard_factory (id,
                                                       skey,
                                                       rival_company_id,
                                                       info_id,
                                                       belong_type,
                                                       favoured_policy,
                                                       settlement_cycle,
                                                       pay_way,
                                                       remarks,
                                                       evaluate_quality,
                                                       evaluate_delivery,
                                                       evaluate_price,
                                                       evaluate_service,
                                                       evaluate_account_period,
                                                       evaluate_other)
        VALUES (#{id},
                #{skey},
                #{rivalCompanyId},
                #{infoId},
                #{belongType},
                #{favouredPolicy, typeHandler=com.djcps.djeasyorder.common.typehandler.CustomerFavouredPolicyTypeHandler},
                #{settlementCycle},
                #{payWay,typeHandler=com.djcps.djeasyorder.common.typehandler.StringToListTypeHandler},
                #{remarks},
                #{evaluate.evaluateQuality},
                #{evaluate.evaluateDelivery},
                #{evaluate.evaluatePrice},
                #{evaluate.evaluateService},
                #{evaluate.evaluateAccountPeriod},
                #{evaluate.evaluateOther, typeHandler=com.djcps.djeasyorder.common.typehandler.IntegerListTypeHandler})
    </insert>

    <select id="getListByInfoId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_cooperative_cardboard_factory
        where info_id = #{infoId}
        and skey = #{companyId}
        and isdel = 0
    </select>
    
    <select id="getPoByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_cooperative_cardboard_factory
        where skey = #{companyId}
        and isdel = 0
        <if test="infoId != null and infoId != ''">
            AND info_id = #{infoId}
        </if>
        <if test="rivalCompanyId != null and rivalCompanyId != ''">
            AND rival_company_id = #{rivalCompanyId}
        </if>
        <if test="rivalCompanyIdList != null and rivalCompanyIdList.size() > 0">
            AND rival_company_id IN
            <foreach collection="rivalCompanyIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="belongType != null">
            AND belong_type = #{belongType}
        </if>
    </select>

    <select id="getInfoById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_cooperative_cardboard_factory
        where skey = #{companyId}
        and isdel = 0
        AND id = #{id}
    </select>

    <update id="updateNotNull">
        update crm_cooperative_cardboard_factory
        <set>
            <if test="rivalCompanyId != null">
                rival_company_id = #{rivalCompanyId},
            </if>
            <if test="infoId != null">
                info_id = #{infoId},
            </if>
            <if test="belongType != null">
                belong_type = #{belongType},
            </if>
            <if test="favouredPolicy != null">
                favoured_policy =
                #{favouredPolicy, typeHandler=com.djcps.djeasyorder.common.typehandler.CustomerFavouredPolicyTypeHandler},
            </if>
            <if test="settlementCycle != null">
                settlement_cycle = #{settlementCycle},
            </if>
            <if test="payWay != null">
                pay_way = #{payWay},
            </if>
            <if test="remarks != null">
                remarks = #{remarks},
            </if>
            <if test="evaluate.evaluateQuality != null">
                evaluate_quality = #{evaluate.evaluateQuality},
            </if>
            <if test="evaluate.evaluateDelivery != null">
                evaluate_delivery = #{evaluate.evaluateDelivery},
            </if>
            <if test="evaluate.evaluatePrice != null">
                evaluate_price = #{evaluate.evaluatePrice},
            </if>
            <if test="evaluate.evaluateService != null">
                evaluate_service = #{evaluate.evaluateService},
            </if>
            <if test="evaluate.evaluateAccountPeriod != null">
                evaluate_account_period = #{evaluate.evaluateAccountPeriod},
            </if>
            <if test="evaluate.evaluateOther != null">
                evaluate_other = #{evaluate.evaluateOther, typeHandler=com.djcps.djeasyorder.common.typehandler.IntegerListTypeHandler},
            </if>
        </set>
        where id = #{id}
        and skey = #{companyId}
        and isdel = 0
    </update>

    <update id="update">
        update crm_cooperative_cardboard_factory
        set
          rival_company_id = #{rivalCompanyId},
          favoured_policy = #{favouredPolicy, typeHandler=com.djcps.djeasyorder.common.typehandler.CustomerFavouredPolicyTypeHandler},
          settlement_cycle = #{settlementCycle},
          pay_way = #{payWay},
          remarks = #{remarks},
          evaluate_quality = #{evaluate.evaluateQuality},
          evaluate_delivery = #{evaluate.evaluateDelivery},
          evaluate_price = #{evaluate.evaluatePrice},
          evaluate_service = #{evaluate.evaluateService},
          evaluate_account_period = #{evaluate.evaluateAccountPeriod},
          evaluate_other = #{evaluate.evaluateOther, typeHandler=com.djcps.djeasyorder.common.typehandler.IntegerListTypeHandler}
        where id = #{id}
        and skey = #{companyId}
        and isdel = 0
  </update>

    <update id="updateHideField">
        update crm_cooperative_cardboard_factory
        <set>
            <if test="rivalCompanyId.hide == false">
                rival_company_id = #{rivalCompanyId.value},
            </if>
            <if test="favouredPolicy.hide == false">
                favoured_policy =
                #{favouredPolicy.value, typeHandler=com.djcps.djeasyorder.common.typehandler.CustomerFavouredPolicyTypeHandler},
            </if>
            <if test="settlementCycle.hide == false">
                settlement_cycle = #{settlementCycle.value},
            </if>
            <if test="payWay.hide == false">
                pay_way = #{payWay.value},
            </if>
            <if test="remarks.hide == false">
                remarks = #{remarks.value},
            </if>
            <if test="evaluate.hide == false">
                evaluate_quality = #{evaluate.value.evaluateQuality},
            </if>
            <if test="evaluate.hide == false">
                evaluate_delivery = #{evaluate.value.evaluateDelivery},
            </if>
            <if test="evaluate.hide == false">
                evaluate_price = #{evaluate.value.evaluatePrice},
            </if>
            <if test="evaluate.hide == false">
                evaluate_service = #{evaluate.value.evaluateService},
            </if>
            <if test="evaluate.hide == false">
                evaluate_account_period = #{evaluate.value.evaluateAccountPeriod},
            </if>
            <if test="evaluate.hide == false">
                evaluate_other = #{evaluate.value.evaluateOther, typeHandler=com.djcps.djeasyorder.common.typehandler.IntegerListTypeHandler},
            </if>
        </set>
        where id = #{id}
        and skey = #{companyId}
        and isdel = 0
    </update>

    <update id="del">
        update crm_cooperative_cardboard_factory
        set
        isdel = 1, isdel_per = #{operatorId}
        where id = #{id}
        and isdel = 0
        and skey = #{companyId}
    </update>

    <select id="getListByInfoIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_cooperative_cardboard_factory
        where info_id in
        <foreach collection="infoIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and isdel = 0
    </select>
</mapper>