package com.djcps.djeasyorder.cooperativecardboardfactory.msg;

import com.djcps.djeasyorder.common.constant.SystemCodeEnum;
import com.djcps.framework.httpformat.message.MsgInterface;

/**
 * <AUTHOR>
 * @create 2022-10-31 14:06
 */
public enum CooperativeCardboardFactoryMsg implements MsgInterface {
    /**
     * msg
     */
    合作纸板厂重复添加("001","合作纸板厂重复添加"),
    无法获取合作纸板厂信息("002","无法获取合作纸板厂信息"),
    ;

    /**
     * 代码code
     */
    private final String code;
    /**
     * 信息内容
     */
    private final String msg;

    CooperativeCardboardFactoryMsg(String code, String msg) {
        this.code = SystemCodeEnum.SYS_CODE + SystemCodeEnum.COOPERATIVE_CARDBOARD_FACTORY + code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

}