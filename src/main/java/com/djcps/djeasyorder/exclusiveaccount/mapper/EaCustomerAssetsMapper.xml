<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.djcps.djeasyorder.exclusiveaccount.dao.EaCustomerAssetsDao">
  <resultMap id="BaseResultMap" type="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo">
    <!--@mbg.generated-->
    <!--@Table ea_customer_assets-->
    <id column="rowid" property="rowid" />
    <result column="id" property="id" />
    <result column="isdel" property="isdel" />
    <result column="isdel_per" property="isdelPer" />
    <result column="skey" property="skey" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="customer_id" property="customerId" />
    <result column="customer_name" property="customerName" />
    <result column="assets_type" property="assetsType" />
    <result column="assets_name" property="assetsName" />
    <result column="newness_oldness" property="newnessOldness" />
    <result column="newness_oldness_remark" property="newnessOldnessRemark" />
    <result column="purchase_price" property="purchasePrice" />
    <result column="estimated_price" property="estimatedPrice" />
    <result column="score_value" property="scoreValue" />
    <result column="operator_id" property="operatorId" />
    <result column="operator_name" property="operatorName" />
    <result property="fileList" column="file" jdbcType="VARCHAR"
            typeHandler="com.djcps.djeasyorder.common.typehandler.FileListTypeHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `rowid`, id, isdel, isdel_per, skey, create_time, update_time, customer_id, customer_name,
    assets_type, assets_name, newness_oldness, newness_oldness_remark, purchase_price, 
    estimated_price, `file`, score_value, operator_id, operator_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
      <!--@mbg.generated-->
      select
      <include refid="Base_Column_List"/>
      from ea_customer_assets
      where `id` = #{id}
        and isdel = 0
  </select>
  <delete id="deleteByPrimaryKey">
    update ea_customer_assets
    set isdel     = #{timestamp},
        isdel_per = #{delPer}
    where `id` = #{id}
      and isdel = 0
      and skey = #{skey}
  </delete>
  <insert id="insert" keyColumn="rowid" keyProperty="rowid" parameterType="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ea_customer_assets (id, isdel, isdel_per, skey, create_time, update_time, customer_id,
      customer_name, assets_type, assets_name, newness_oldness, newness_oldness_remark, 
      purchase_price, estimated_price, `file`, score_value, operator_id, operator_name
      )
    values (#{id}, #{isdel}, #{isdelPer}, #{skey}, #{createTime}, #{updateTime}, #{customerId},
      #{customerName}, #{assetsType}, #{assetsName}, #{newnessOldness}, #{newnessOldnessRemark}, 
      #{purchasePrice}, #{estimatedPrice}, #{fileList, typeHandler=com.djcps.djeasyorder.common.typehandler.FileListTypeHandler}, #{scoreValue}, #{operatorId}, #{operatorName}
      )
  </insert>
  <insert id="insertSelective" keyColumn="rowid" keyProperty="rowid" parameterType="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ea_customer_assets
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="isdel != null">
        isdel,
      </if>
      <if test="isdelPer != null">
        isdel_per,
      </if>
      <if test="skey != null">
        skey,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="assetsType != null">
        assets_type,
      </if>
      <if test="assetsName != null">
        assets_name,
      </if>
      <if test="newnessOldness != null">
        newness_oldness,
      </if>
      <if test="newnessOldnessRemark != null">
        newness_oldness_remark,
      </if>
      <if test="purchasePrice != null">
        purchase_price,
      </if>
      <if test="estimatedPrice != null">
        estimated_price,
      </if>
      <if test="fileList != null">
        `file`,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="isdel != null">
        #{isdel},
      </if>
      <if test="isdelPer != null">
        #{isdelPer},
      </if>
      <if test="skey != null">
        #{skey},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="customerId != null">
        #{customerId},
      </if>
      <if test="customerName != null">
        #{customerName},
      </if>
      <if test="assetsType != null">
        #{assetsType},
      </if>
      <if test="assetsName != null">
        #{assetsName},
      </if>
      <if test="newnessOldness != null">
        #{newnessOldness},
      </if>
      <if test="newnessOldnessRemark != null">
        #{newnessOldnessRemark},
      </if>
      <if test="purchasePrice != null">
        #{purchasePrice},
      </if>
      <if test="estimatedPrice != null">
        #{estimatedPrice},
      </if>
      <if test="fileList != null">
        #{fileList, typeHandler=com.djcps.djeasyorder.common.typehandler.FileListTypeHandler},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="operatorId != null">
        #{operatorId},
      </if>
      <if test="operatorName != null">
        #{operatorName},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo">
    <!--@mbg.generated-->
    update ea_customer_assets
    <set>
      <if test="id != null">
        id = #{id},
      </if>
      <if test="isdel != null">
        isdel = #{isdel},
      </if>
      <if test="isdelPer != null">
        isdel_per = #{isdelPer},
      </if>
      <if test="skey != null">
        skey = #{skey},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName},
      </if>
      <if test="assetsType != null">
        assets_type = #{assetsType},
      </if>
      <if test="assetsName != null">
        assets_name = #{assetsName},
      </if>
      <if test="newnessOldness != null">
        newness_oldness = #{newnessOldness},
      </if>
      <if test="newnessOldnessRemark != null">
        newness_oldness_remark = #{newnessOldnessRemark},
      </if>
      <if test="purchasePrice != null">
        purchase_price = #{purchasePrice},
      </if>
      <if test="estimatedPrice != null">
        estimated_price = #{estimatedPrice},
      </if>
      <if test="fileList != null">
        file = #{fileList, typeHandler=com.djcps.djeasyorder.common.typehandler.FileListTypeHandler},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName},
      </if>
    </set>
    where `id` = #{id}
    and isdel = 0
    and skey = #{skey}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo">
    <!--@mbg.generated-->
    update ea_customer_assets
    set id = #{id},
      isdel = #{isdel},
      isdel_per = #{isdelPer},
      skey = #{skey},
      create_time = #{createTime},
      update_time = #{updateTime},
      customer_id = #{customerId},
      customer_name = #{customerName},
      assets_type = #{assetsType},
      assets_name = #{assetsName},
      newness_oldness = #{newnessOldness},
      newness_oldness_remark = #{newnessOldnessRemark},
      purchase_price = #{purchasePrice},
      estimated_price = #{estimatedPrice},
      `file` = #{file},
      score_value = #{scoreValue},
      operator_id = #{operatorId},
      operator_name = #{operatorName}
    where  `id` = #{id}
    and isdel = 0
    and skey = #{skey}
  </update>
  <insert id="batchInsert" keyColumn="rowid" keyProperty="rowid" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ea_customer_assets
    (id, isdel, isdel_per, skey, create_time, update_time, customer_id, customer_name,
      assets_type, assets_name, newness_oldness, newness_oldness_remark, purchase_price, 
      estimated_price, `file`, score_value, operator_id, operator_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.isdel}, #{item.isdelPer}, #{item.skey}, #{item.createTime}, #{item.updateTime}, 
        #{item.customerId}, #{item.customerName}, #{item.assetsType}, #{item.assetsName},
        #{item.newnessOldness}, #{item.newnessOldnessRemark}, #{item.purchasePrice}, #{item.estimatedPrice}, 
        #{item.file}, #{item.scoreValue}, #{item.operatorId}, #{item.operatorName})
    </foreach>
  </insert>

  <select id="selectCustomerAssetsCount" resultType="int">
    select count(*)
    from ea_customer_assets
    <where>
      isdel = 0
      and skey = #{realCompanyId}
      and customer_id = #{customerId}
    </where>
  </select>

  <select id="selectCustomerAssetsList" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from ea_customer_assets
    <where>
      isdel = 0
      and skey = #{realCompanyId}
      and customer_id = #{customerId}
    </where>
    order by update_time desc
  </select>


  <select id="getByEaCustomerIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from ea_customer_assets
    where isdel = 0
    and customer_id in
    <foreach item="customerId" collection="customerIds" open="(" separator="," close=")">
      #{customerId}
    </foreach>
  </select>

  <update id="updateByIdChange" parameterType="com.djcps.djeasyorder.exclusiveaccount.model.po.EaCustomerAssetsPo">
    <!--@mbg.generated-->
    update ea_customer_assets
    <set>
      <if test="id != null">
        id = #{id},
      </if>
      <if test="isdel != null">
        isdel = #{isdel},
      </if>
      <if test="isdelPer != null">
        isdel_per = #{isdelPer},
      </if>
      <if test="skey != null">
        skey = #{skey},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
        update_time = NOW(),
      <if test="customerId != null">
        customer_id = #{customerId},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName},
      </if>
      <if test="assetsType != null">
        assets_type = #{assetsType},
      </if>
      <if test="assetsName != null">
        assets_name = #{assetsName},
      </if>
        newness_oldness = #{newnessOldness},
        newness_oldness_remark = #{newnessOldnessRemark},
        purchase_price = #{purchasePrice},
        estimated_price = #{estimatedPrice},
        file = #{fileList, typeHandler=com.djcps.djeasyorder.common.typehandler.FileListTypeHandler},
        score_value = #{scoreValue},
      <if test="operatorId != null">
        operator_id = #{operatorId},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName},
      </if>
    </set>
    where `id` = #{id}
    and isdel = 0
    and skey = #{skey}
  </update>
</mapper>