package com.djcps.djeasyorder.intelligentcustomer.infrastructure;

import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Iterator;
import java.util.function.Function;

/**
 * Mongo分页查询迭代器
 */
public class MongoPageIterator<T, R> implements Iterator<R> {

    private long currentPage = 0;
    private Iterator<T> cache = null;
    private final Query query;
    private final MongoTemplate mongoTemplate;
    private final Scheme<T> scheme;
    private final Function<T, R> convertor;
    private boolean end = false;

    public MongoPageIterator(MongoTemplate mongoTemplate, Scheme<T> scheme, Query query, Function<T, R> converter) {
        this.mongoTemplate = mongoTemplate;
        this.query = query;
        this.scheme = scheme;
        this.convertor = converter;
        fetchNextPage();
    }

    private void fetchNextPage() {
        // 一次拉取的数量
        int cacheSize = 1000;
        query.skip((currentPage++) * cacheSize).limit(cacheSize);
        this.cache = mongoTemplate.stream(query, scheme.getModelClass(), scheme.getTableName());
    }

    @Override
    public boolean hasNext() {
        if (end) {
            return false;
        }
        boolean hasNext = this.cache.hasNext();
        if (!hasNext) {
            fetchNextPage();
            hasNext = this.cache.hasNext();
            if (!hasNext) {
                this.end = true;
            }
            return hasNext;
        }
        return true;
    }

    @Override
    public R next() {
        return convertor.apply(this.cache.next());
    }
}