<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dao.IntelligentCustomerDao">

    <!-- 随机生成32位的uuid -->
    <sql id="UUIDSql">
		REPLACE(UUID(), '-', '')
	</sql>

    <sql id="CrmIntelligentCustomerPoSql" >
          crm_intelligent_customer.id AS id,
          crm_intelligent_customer.source AS source,
          crm_intelligent_customer.mongo_id AS mongoId,
          crm_intelligent_customer.uri AS uri,
          crm_intelligent_customer.mongo_create_time AS mongoCreateTime,
          crm_intelligent_customer.mongo_update_time AS mongoUpdateTime,
          crm_intelligent_customer.customer_name AS customerName,
          crm_intelligent_customer.customer_address AS customerAddress,
          crm_intelligent_customer.detail_address AS detailAddress,
          crm_intelligent_customer.longitude_and_latitude AS longitudeAndLatitude,
          crm_intelligent_customer.contact_way AS contactWay,
          crm_intelligent_customer.city_code AS cityCode,
          crm_intelligent_customer.street_code AS streetCode,
          crm_intelligent_customer.keywords,
          crm_intelligent_customer.more_contacts
    </sql>

    <sql id="selectAllColumns">
        select t1.id,
               t1.skey,
               t1.source,
               t1.mongo_id mongoId,
               t1.uri,
               t1.mongo_create_time mongoCreateTime,
               t1.mongo_update_time mongoUpdateTime,
               t1.mongo_table_name mongoTableName,
               t1.customer_name customerName,
               t1.customer_address customerAddress,
               t1.detail_address detailAddress,
               t1.longitude_and_latitude longitudeAndLatitude,
               t1.contact_way contactWay,
               t1.more_contacts moreContacts,
               t1.city_code cityCode,
               t1.street_code streetCode,
               t1.keywords
        from crm_intelligent_customer t1
    </sql>

    <insert id="insert">
        INSERT INTO crm_intelligent_customer (
        id,
        skey,
        source,
        mongo_id,
        <if test="uri != null and uri != ''">
            uri,
        </if>
        mongo_create_time,
        mongo_update_time,
        mongo_table_name,
        <if test="customerName != null and customerName != ''">
            customer_name,
        </if>
        <if test="customerAddress != null and customerAddress != ''">
            customer_address,
        </if>
        <if test="detailAddress != null and detailAddress != ''">
            detail_address,
        </if>
        <if test="longitudeAndLatitude != null and longitudeAndLatitude != ''">
            longitude_and_latitude,
        </if>
        <if test="contactWay != null and contactWay != ''">
            contact_way,
        </if>
        <if test="moreContacts != null">
            more_contacts,
        </if>
        city_code,
        <if test="streetCode != null">
            street_code,
        </if>
        keywords
        )
        VALUES
        (
        #{id},
        #{skey},
        #{source},
        #{mongoId},
        <if test="uri != null and uri != ''">
            #{uri},
        </if>
        #{mongoCreateTime},
        #{mongoUpdateTime},
        #{mongoTableName},
        <if test="customerName != null and customerName != ''">
            #{customerName},
        </if>
        <if test="customerAddress != null and customerAddress != ''">
            #{customerAddress},
        </if>
        <if test="detailAddress != null and detailAddress != ''">
            #{detailAddress},
        </if>
        <if test="longitudeAndLatitude != null and longitudeAndLatitude != ''">
            #{longitudeAndLatitude},
        </if>
        <if test="contactWay != null and contactWay != ''">
            #{contactWay},
        </if>
        <if test="moreContacts != null">
            #{moreContacts},
        </if>
        #{cityCode},
        <if test="streetCode != null">
            #{streetCode},
        </if>
         #{keywords}
        )
    </insert>

    <select id="findById"
            resultType="com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerDO">
        <include refid="selectAllColumns"/>
        where t1.isdel = 0 and t1.id = #{id}
    </select>

    <select id="findByClueId"
            resultType="com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerDO">
        <include refid="selectAllColumns"/>
        right join crm_intelligent_customer_recommend r on t1.id = r.crm_intelligent_customer_id
        where r.clue_id = #{clueId}
    </select>

    <select id="getByPhone" resultMap="moreContactsResultMap">
        SELECT
        <include refid="CrmIntelligentCustomerPoSql"/>
        FROM
          crm_intelligent_customer
        WHERE contact_way = #{contactWay}
          AND isdel = 0
    </select>

    <select id="getByMorePhone" resultMap="moreContactsResultMap">
        SELECT
        <include refid="CrmIntelligentCustomerPoSql"/>
        FROM
          crm_intelligent_customer
        WHERE more_contacts = #{moreContacts}
          AND isdel = 0
    </select>

    <select id="getByCustomerName" resultMap="moreContactsResultMap">
        SELECT
        <include refid="CrmIntelligentCustomerPoSql"/>
        FROM
          crm_intelligent_customer
        WHERE customer_name = #{name}
        AND isdel = 0
    </select>

    <select id="getIntelligentRecommendCustomerList" resultMap="moreContactsResultMap">
        SELECT 
          <include refid="CrmIntelligentCustomerPoSql"/>
        FROM
          crm_intelligent_customer 
        WHERE crm_intelligent_customer.city_code = #{supplierCityCode}
        <if test="intelligentCustomerId != null and intelligentCustomerId != ''">
            AND id = #{intelligentCustomerId}
        </if>
        AND crm_intelligent_customer.isdel = 0
        <if test="phoneList != null and phoneList.size() > 0" >
            AND crm_intelligent_customer.contact_way NOT IN
            <foreach collection="phoneList" item="phone" index="index" open="(" close=")" separator=",">
                #{phone}
            </foreach>
        </if>
          AND crm_intelligent_customer.id NOT IN
          (SELECT 
            crm_intelligent_customer_recommend.crm_intelligent_customer_id 
          FROM
            crm_intelligent_customer_recommend 
          WHERE crm_intelligent_customer_recommend.supplier_id = #{supplierId}
            AND crm_intelligent_customer_recommend.isdel = 0)
        LIMIT #{recommendNum}
    </select>

    <resultMap id="moreContactsResultMap" type="com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerPo">
        <result column="more_contacts" property="moreContacts" typeHandler="com.djcps.djeasyorder.common.typehandler.MoreContactsTypeHandler"/>
    </resultMap>

    <insert id="insertIntelligentCustomerRecommend">
        INSERT INTO crm_intelligent_customer_recommend (
        id,
        skey,
        clue_id,
        crm_intelligent_customer_id,
        supplier_id
        )
        VALUES
        (
        <include refid="UUIDSql"/>,
        '100',
        #{clueId},
        #{crmIntelligentCustomerId},
        #{supplierId}
        )

    </insert>

    <update id="delByMongoId">
        UPDATE
          crm_intelligent_customer
        SET
          isdel = 1
        WHERE mongo_id = #{mongoId}
    </update>

    <update id="delIntelligentCustomerForKeyword">
        UPDATE
          crm_intelligent_customer
        SET
          isdel = 1
        WHERE customer_name REGEXP #{keywords}
    </update>

</mapper>