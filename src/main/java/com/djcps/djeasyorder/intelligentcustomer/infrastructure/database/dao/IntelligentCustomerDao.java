package com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dao;

import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerDO;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerPo;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomerRecommend;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 智能获客 持久层
 *
 * <AUTHOR>
 * @create 2021-05-11 16:47
 */
@Repository
public interface IntelligentCustomerDao {

    /**
     * 通过手机号获取客户信息
     *
     * @param contactWay 手机号
     * @return 客户信息
     * <AUTHOR>
     * @date 2021/5/11 17:03
     */
    CrmIntelligentCustomerPo getByPhone(@Param("contactWay") String contactWay);

    /**
     * 通过更多号码获取客户信息
     *
     * @param moreContacts 更多号码
     * @return 客户信息
     * <AUTHOR>
     * @date 2021/5/11 17:14
     */
    CrmIntelligentCustomerPo getByMorePhone(@Param("moreContacts") String moreContacts);

    /**
     * 保存到数据库
     *
     * @param customerDO 持久化对象
     */
    void insert(CrmIntelligentCustomerDO customerDO);

    /**
     * 查询智能获客数据
     *
     * @param id 智能获客的id
     * @return 智能获客数据
     */
    CrmIntelligentCustomerDO findById(@Param("id") String id);

    /**
     * 查询智能获客数据
     *
     * @param clueId 线索id
     * @return 智能获客数据
     */
    CrmIntelligentCustomerDO findByClueId(@Param("clueId") String clueId);

    /**
     * 获取智能推荐的客户列表
     *
     * @param supplierId       供应商id
     * @param supplierCityCode 供应商所在城市
     * @param phoneList        已存在客户/线索的手机号列表
     * @param recommendNum     推荐数量
     * @param id               智能获客表id
     * @return 智能获客列表
     * <AUTHOR>
     * @date 2021/5/13 13:29
     */
    List<CrmIntelligentCustomerPo> getIntelligentRecommendCustomerList(@Param("supplierId") String supplierId,
                                                                       @Param("supplierCityCode") Integer supplierCityCode,
                                                                       @Param("phoneList") List<String> phoneList,
                                                                       @Param("recommendNum") int recommendNum,
                                                                       @Param("intelligentCustomerId") String id);

    /**
     * 记录智能获客推荐关系表
     *
     * @param insertModel 插入模型
     * <AUTHOR>
     * @date 2021/5/13 15:10
     */
    void insertIntelligentCustomerRecommend(IntelligentCustomerRecommend insertModel);

    /**
     * 通过mongoId删除数据
     *
     * @param mongoId mongoId
     * <AUTHOR>
     * @date 2021/5/20 15:41
     */
    void delByMongoId(@Param("mongoId") String mongoId);

    /**
     * 根据关键字删除智能获客数据
     *
     * @param keywords 过滤关键字
     * <AUTHOR>
     * @date 2021/6/8 9:05
     */
    void delIntelligentCustomerForKeyword(@Param("keywords") String keywords);

    /**
     * 根据名字获取客户
     *
     * @param name 客户名称
     * @return 客户list
     * <AUTHOR>
     * @date 2021/7/21 11:08
     */
    List<CrmIntelligentCustomerPo> getByCustomerName(@Param("name") String name);
}