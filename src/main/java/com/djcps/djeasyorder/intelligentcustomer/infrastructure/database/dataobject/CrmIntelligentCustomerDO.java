package com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject;

import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.Instant;

/**
 * 智能获客客户的持久化模型
 */
@Data
public class CrmIntelligentCustomerDO {

    /**
     * id
     */
    private String id;
    /**
     * 拆分键
     */
    private String skey;
    /**
     * 来源
     */
    private Integer source;
    /**
     * mongodb唯一id
     */
    private String mongoId;
    /**
     * 链接地址
     */
    private String uri;
    /**
     * mongodb中数据创建时间
     */
    private Instant mongoCreateTime;
    /**
     * mongodb中数据修改时间
     */
    private Instant mongoUpdateTime;
    /**
     * mongodb表名
     */
    private String mongoTableName;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户地址
     */
    private String customerAddress;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 经纬度
     */
    private String longitudeAndLatitude;
    /**
     * 联系方式
     */
    private String contactWay;
    /**
     * 更多联系人
     */
    private String moreContacts;
    /**
     * 城市code码
     */
    private Integer cityCode;
    /**
     * 街道code码
     */
    private Integer streetCode;
    /**
     * 关键词
     */
    private String keywords;

    public void setKeywords(String keywords) {
        this.keywords = StringUtils.isEmpty(keywords) ? "" : keywords;
    }
}
