package com.djcps.djeasyorder.intelligentcustomer.infrastructure;

import com.djcps.djeasyorder.common.utils.Caller;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import com.djcps.djeasyorder.intelligentcustomer.domain.gateway.IntelligentCustomerGateway;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.IntelligentCustomerMetadata;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.convertor.IntelligentCustomerConvertor;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dao.IntelligentCustomerDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Iterator;

/**
 * 智能获客的数据门户的实现类
 */
@Component
public class IntelligentCustomerGatewayImpl implements IntelligentCustomerGateway {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IntelligentCustomerDao intelligentCustomerDao;

    @Override
    public void insert(IntelligentCustomer intelligentCustomer) {
        intelligentCustomerDao.insert(IntelligentCustomerConvertor.toDataObject(intelligentCustomer));
    }

    @Override
    public void delete(String id) {
        intelligentCustomerDao.delByMongoId(id);
    }

    @Override
    public IntelligentCustomer findById(String id) {
        return IntelligentCustomerConvertor.toEntity(intelligentCustomerDao.findById(id));
    }

    @Override
    public IntelligentCustomer findByClueId(String clueId) {
        return Caller.letGet(intelligentCustomerDao.findByClueId(clueId), IntelligentCustomerConvertor::toEntity);
    }

    @Override
    public <T extends IntelligentCustomerMetadata> Iterator<T> iterator(Scheme<T> scheme, LocalDate date) {
        Query query = new Query();
        if (!ObjectUtils.isEmpty(date)) {
            // 如果日期条件不为空
            Long minTime = LocalDateTime.of(date, LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000L;
            Long maxTime = LocalDateTime.of(date, LocalTime.MAX).toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000L;
            Criteria criteria = new Criteria();
            // 获取创建时间在昨天 或者 更新时间在昨天的数据
            criteria.orOperator(Criteria.where("create_time").gte(minTime).lt(maxTime), Criteria.where("update_time").gte(minTime).lt(maxTime));
            query.addCriteria(criteria);
        }
        return new MongoPageIterator<>(mongoTemplate, scheme, query, it -> {
            it.setTableName(scheme.getTableName());
            return it;
        });
    }
}
