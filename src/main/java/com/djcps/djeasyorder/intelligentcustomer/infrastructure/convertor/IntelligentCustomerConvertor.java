package com.djcps.djeasyorder.intelligentcustomer.infrastructure.convertor;

import com.alibaba.fastjson.JSON;
import com.djcps.djeasyorder.common.utils.Caller;
import com.djcps.djeasyorder.customer.model.po.expand.LocationPoint;
import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsModel;
import com.djcps.djeasyorder.intelligentcustomer.domain.DataChannel;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerDO;
import org.springframework.beans.BeanUtils;

/**
 * 智能获客的客户转换器
 */
public class IntelligentCustomerConvertor {

    /**
     * 将客户转换成持久化模型
     * @param customer 客户
     * @return 持久化模型
     */
    public static CrmIntelligentCustomerDO toDataObject(IntelligentCustomer customer) {
        final CrmIntelligentCustomerDO customerDO = new CrmIntelligentCustomerDO();
        customerDO.setId(customer.getId());
        customerDO.setSkey(customer.getCompanyId());
        customerDO.setSource(customer.getChannel().getCode());
        customerDO.setMongoId(customer.getMetadataId());
        customerDO.setUri(customer.getUri());
        customerDO.setMongoCreateTime(customer.getMetadataCreateTime());
        customerDO.setMongoUpdateTime(customer.getMetadataUpdateTime());
        customerDO.setMongoTableName(customer.getMetadataTableName());
        customerDO.setCustomerName(customer.getCustomerName());
        customerDO.setCustomerAddress(customer.getCustomerAddress());
        customerDO.setDetailAddress(customer.getDetailAddress());
        customerDO.setLongitudeAndLatitude(Caller.letGet(customer.getLongitudeAndLatitude(), LocationPoint::toArrayString));
        customerDO.setContactWay(customer.getContactWay());
        customerDO.setMoreContacts(Caller.letGet(customer.getMoreContacts(), JSON::toJSONString));
        customerDO.setCityCode(customer.getCityCode());
        customerDO.setStreetCode(customer.getStreetCode());
        customerDO.setKeywords(customer.getKeywords());
        return customerDO;
    }

    public static IntelligentCustomer toEntity(CrmIntelligentCustomerDO customerDO) {
        IntelligentCustomer customer = new IntelligentCustomer();
        BeanUtils.copyProperties(customerDO, customer);
        customer.setCompanyId(customerDO.getSkey());
        customer.setChannel(DataChannel.valueOfCode(customerDO.getSource()));
        customer.setMetadataId(customerDO.getMongoId());
        customer.setMetadataCreateTime(customerDO.getMongoCreateTime());
        customer.setMetadataUpdateTime(customerDO.getMongoUpdateTime());
        customer.setMetadataTableName(customerDO.getMongoTableName());
        customer.setLongitudeAndLatitude(LocationPoint.byArrayString(customerDO.getLongitudeAndLatitude()));
        customer.setMoreContacts(Caller.letGet(customerDO.getMoreContacts(), it -> JSON.parseArray(it, MoreContactsModel.class)));
        return customer;
    }
}
