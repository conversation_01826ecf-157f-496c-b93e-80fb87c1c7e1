package com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject;

import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsModel;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/12 16:19
 */
@Data
public class CrmIntelligentCustomerPo {

    /**
     * id
     */
    private String id;

    /**
     * 来源(0:高德地图)
     */
    private Integer source;

    /**
     * mongodb唯一id
     */
    private String mongoId;

    /**
     * 链接地址
     */
    private String uri;

    /**
     * mongodb中数据创建时间
     */
    private Timestamp mongoCreateTime;

    /**
     * mongodb中数据修改时间
     */
    private Timestamp mongoUpdateTime;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 经纬度
     */
    private String longitudeAndLatitude;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 更多联系人
     */
    private List<MoreContactsModel> moreContacts;

    /**
     * 城市code码
     */
    private Integer cityCode;

    /**
     * 街道code码
     */
    private Integer streetCode;

    /**
     * 关键词
     */
    private String keywords;
}
