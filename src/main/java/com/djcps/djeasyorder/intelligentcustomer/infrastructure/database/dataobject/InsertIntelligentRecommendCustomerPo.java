package com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject;

import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsInterface;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @create 2021-05-13 14:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InsertIntelligentRecommendCustomerPo extends CrmIntelligentCustomerPo implements MoreContactsInterface {
    /**
     * 线索id
     */
    private String clueId;
    /**
     * 供应商id
     */
    private String supplierId;
    /**
     * 创建人id
     */
    private String creatorId;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 省code码
     */
    private String provinceCode;
    /**
     * 省code码
     */
    private String areaCode;

    /**
     * 获取省code码
     */
    public Integer getProvinceCode() {
        return Integer.valueOf(super.getStreetCode().toString().substring(0, 2));
    }

    /**
     * 获取区code码
     */
    public Integer getAreaCode() {
        return Integer.valueOf(super.getStreetCode().toString().substring(0, 6));
    }
}
