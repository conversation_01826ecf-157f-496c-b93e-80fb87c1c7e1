package com.djcps.djeasyorder.intelligentcustomer.adapter;

import com.djcps.djeasyorder.common.controller.BaseController;
import com.djcps.djeasyorder.intelligentcustomer.client.api.CustomerRecommendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2021-05-11 9:44
 */
@RestController
@RequestMapping(value = "mongoController", produces = "application/json;charset=utf-8")
public class IntelligentCustomerController extends BaseController {

    @Autowired
    private CustomerRecommendService intelligentCustomerService;

}