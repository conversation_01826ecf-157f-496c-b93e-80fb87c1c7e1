package com.djcps.djeasyorder.intelligentcustomer.domain;

import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.IntelligentCustomerMetadata;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.AmapScheme;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.BaiduScheme;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;
import lombok.Getter;

/**
 * 数据来源渠道
 */
@Getter
public enum DataChannel {

    AMAP(0, "高德地图", AmapScheme.values()),
    BAIDU(1, "百度地图", BaiduScheme.values()),
    ;

    /**
     * 渠道编号
     */
    private final int code;
    /**
     * 渠道名称
     */
    private final String name;
    /**
     * 数据表数组
     */
    private final Scheme<? extends IntelligentCustomerMetadata>[] modelClass;

    DataChannel(int code, String name, Scheme<? extends IntelligentCustomerMetadata>[] modelClass) {
        this.code = code;
        this.name = name;
        this.modelClass = modelClass;
    }

    public static DataChannel valueOfCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DataChannel value : DataChannel.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
