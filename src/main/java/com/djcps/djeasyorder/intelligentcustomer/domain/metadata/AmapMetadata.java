package com.djcps.djeasyorder.intelligentcustomer.domain.metadata;

import com.djcps.djeasyorder.common.utils.Caller;
import com.djcps.djeasyorder.common.utils.RegularUtil;
import com.djcps.djeasyorder.customer.contants.CustomerConstant;
import com.djcps.djeasyorder.customer.model.po.expand.LocationPoint;
import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsModel;
import com.djcps.djeasyorder.intelligentcustomer.domain.DataChannel;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 高德地图数据模型
 *
 * <AUTHOR>
 * @create 2021-05-11 10:18
 */
@Slf4j
@EqualsAndHashCode
@Data
public class AmapMetadata implements IntelligentCustomerMetadata {

    /**
     * id
     */
    private String _id;
    /**
     * 名字
     */
    private String name;
    /**
     * 详细地址（龙岗综合经济开发区振兴化工院内）
     */
    private String address;
    /**
     * 经纬度(通过逗号分隔：117.400614,31.881768)
     */
    private String location;
    /**
     * 联系方式（有可能是固定号码）
     */
    private String tel;
    /**
     * 省名字
     */
    private String pname;
    /**
     * 市名字
     */
    private String cityname;
    /**
     * 区名字
     */
    private String adname;
    /**
     * 区code码
     */
    private String adcode;
    /**
     * 链接地址
     */
    private String uri;
    /**
     * 关键词
     */
    private String match_key;
    /**
     * 创建时间
     */
    private Long create_time;
    /**
     * 修改时间
     */
    private Long update_time;
    /**
     * 是否删除 0或者null 未删除， 1已删除
     */
    private Integer is_delete;
    /**
     * 表名
     */
    private String tableName;

    static final String MORE_PHONE_SPLIT = ";";

    @Override
    public IntelligentCustomer convert() {
        final IntelligentCustomer customer = new IntelligentCustomer();
        // 设置元数据信息
        customer.setChannel(DataChannel.AMAP);
        customer.setMetadataTableName(tableName);
        customer.setMetadataId(get_id());
        customer.setMetadataCreateTime(getCreate_time());
        customer.setMetadataUpdateTime(getUpdate_time() == null ? getCreate_time() : getUpdate_time());
        customer.setIsDelete(getIs_delete());

        customer.setCustomerName(getName());
        customer.setUri(getUri());
        customer.setKeywords(getMatch_key());

        // 设置联系方式信息
        if (StringUtils.hasText(getTel())) {
            String phone = "";
            final String[] telArr = getTel().split(MORE_PHONE_SPLIT);
            if (!ObjectUtils.isEmpty(telArr)) {
                // 手机号
                List<MoreContactsModel> moreContacts = new ArrayList<>();
                for (String s : telArr) {
                    if (ObjectUtils.isEmpty(phone) && RegularUtil.isPhone(s)) {
                        phone = s;
                    } else {
                        if (RegularUtil.isTelOrPhone(s)) {
                            MoreContactsModel moreContact = new MoreContactsModel();
                            moreContact.setPhone(morePhoneCut(s));
                            if (moreContacts.size() < CustomerConstant.MORE_CONTACT_MAX_SIZE) {
                                moreContacts.add(moreContact);
                            } else {
                                break;
                            }
                        }
                    }
                }
                customer.setContactWay(phone);
                customer.setMoreContacts(moreContacts);
            }
        }
        // 设置地址信息
        customer.setCustomerAddress(getPname() + getCityname() + getAdname());
        customer.setCityCode(getCityCode(getAdcode()));
        customer.setDetailAddress(getAddress());
        try {
            customer.setLongitudeAndLatitude(Caller.letGet(getLocation(), LocationPoint::byCsv));
        } catch (Exception ignore) {
        }

        return customer;
    }

    /**
     * 更多手机号截取
     */
    String morePhoneCut(String morePhone) {
        if (ObjectUtils.isEmpty(morePhone)) {
            return "";
        }
        int morePhoneMaxLength = 50;
        if (morePhone.length() > morePhoneMaxLength) {
            morePhone = morePhone.substring(0, morePhoneMaxLength);
        }
        return morePhone;
    }

    /**
     * 从区域码中获取城市code
     */
    private static Integer getCityCode(String areaCodeStr) {
        if (areaCodeStr != null && areaCodeStr.length() == 6 && org.apache.commons.lang.StringUtils.isNumeric(areaCodeStr)) {
            return Integer.parseInt(areaCodeStr.substring(0, 4));
        } else {
            return null;
        }
    }
}
