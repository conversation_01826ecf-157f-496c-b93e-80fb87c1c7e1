package com.djcps.djeasyorder.intelligentcustomer.domain.metadata;

import lombok.Getter;

/**
 * 高德地图表枚举
 *
 * <AUTHOR>
 * @create 2021-05-11 11:03
 */
@Getter
public enum AmapScheme implements Scheme<AmapMetadata> {

    AMAP_API_ANHUI("amap_api_anhui"),
    AMAP_API_BEIJING("amap_api_beijing"),
    AMAP_API_CHONGQING("amap_api_chongqing"),
    AMAP_API_FUJIAN("amap_api_fujian"),
    AMAP_API_GANSU("amap_api_gansu"),
    AMAP_API_GUANGDONG("amap_api_guangdong"),
    AMAP_API_GUANGXI("amap_api_guangxi"),
    AMAP_API_GUIZHOU("amap_api_guizhou"),
    AMAP_API_HEBEI("amap_api_hebei"),
    AMAP_API_HENAN("amap_api_henan"),
    AMAP_API_HUBEI("amap_api_hubei"),
    AMAP_API_HUNAN("amap_api_hunan"),
    AMAP_API_JIANGSU("amap_api_jiangsu"),
    AMAP_API_JIANGXI("amap_api_jiangxi"),
    AMAP_API_LIAONING("amap_api_liaoning"),
    AMAP_API_SHAANXI("amap_api_shaanxi"),
    AMAP_API_SHANDONG("amap_api_shandong"),
    AMAP_API_SHANGHAI("amap_api_shanghai"),
    AMAP_API_SHANXI("amap_api_shanxi"),
    AMAP_API_SICHUAN("amap_api_sichuan"),
    AMAP_API_TIANJIN("amap_api_tianjin"),
    AMAP_API_XIANGGANG("amap_api_xianggang"),
    AMAP_API_ZHEJIANG("amap_api_zhejiang"),
    ;

    private final String tableName;
    private final Class<AmapMetadata> modelClass;

    AmapScheme(String tableName) {
        this.tableName = tableName;
        this.modelClass = AmapMetadata.class;
    }
}
