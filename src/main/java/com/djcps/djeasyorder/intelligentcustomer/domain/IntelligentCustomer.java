package com.djcps.djeasyorder.intelligentcustomer.domain;

import com.djcps.djeasyorder.customer.model.po.expand.LocationPoint;
import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsModel;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.List;

/**
 * 智能获客获取的客户
 */
@Data
public class IntelligentCustomer {

    /**
     * 客户名称的最大长度
     */
    private static final int CUSTOMER_NAME_MAX_LENGTH = 30;
    /**
     * 客户地址的最大长度
     */
    private static final int CUSTOMER_ADDRESS_MAX_LENGTH = 50;
    /**
     * 详细地址的最大长度
     */
    private static final int DETAIL_ADDRESS_MAX_LENGTH = 50;
    /**
     * 关键词的最大长度
     */
    private static final int KEYWORDS_MAX_LENGTH = 100;

    /**
     * id
     */
    private String id;
    /**
     * 公司id
     */
    private String companyId;
    /**
     * 数据来源渠道
     */
    private DataChannel channel;
    /**
     * 元数据的编号
     */
    private String metadataId;
    /**
     * 元数据的表名
     */
    private String metadataTableName;
    /**
     * 元数据的创建时间
     */
    private Instant metadataCreateTime;

    public void setMetadataCreateTime(Long metadataCreateTime) {
        if (metadataCreateTime != null) {
            this.metadataCreateTime = Instant.ofEpochSecond(metadataCreateTime);
        }
    }

    public void setMetadataCreateTime(Instant metadataCreateTime) {
        this.metadataCreateTime = metadataCreateTime;
    }

    /**
     * 元数据的修改时间
     */
    private Instant metadataUpdateTime;

    public void setMetadataUpdateTime(Long metadataUpdateTime) {
        if (metadataUpdateTime != null) {
            this.metadataUpdateTime = Instant.ofEpochSecond(metadataUpdateTime);
        }
    }

    public void setMetadataUpdateTime(Instant metadataUpdateTime) {
        this.metadataUpdateTime = metadataUpdateTime;
    }

    /**
     * 链接地址
     */
    private String uri;
    /**
     * 客户名称
     */
    private String customerName;

    public void setCustomerName(String customerName) {
        if (!ObjectUtils.isEmpty(customerName) && customerName.length() > CUSTOMER_NAME_MAX_LENGTH) {
            customerName = customerName.substring(0, CUSTOMER_NAME_MAX_LENGTH);
        }
        this.customerName = customerName;
    }

    /**
     * 城市code码
     */
    private Integer cityCode;

    /**
     * 街道code码
     */
    private Integer streetCode;
    /**
     * 客户地址
     */
    private String customerAddress;

    public void setCustomerAddress(String customerAddress) {
        if (!ObjectUtils.isEmpty(customerAddress) && customerAddress.length() > CUSTOMER_ADDRESS_MAX_LENGTH) {
            customerAddress = customerAddress.substring(0, CUSTOMER_ADDRESS_MAX_LENGTH);
        }
        this.customerAddress = customerAddress;
    }

    /**
     * 详细地址
     */
    private String detailAddress;

    public void setDetailAddress(String detailAddress) {
        if (!ObjectUtils.isEmpty(detailAddress) && detailAddress.length() > DETAIL_ADDRESS_MAX_LENGTH) {
            detailAddress = detailAddress.substring(0, DETAIL_ADDRESS_MAX_LENGTH);
        }
        this.detailAddress = detailAddress;
    }

    /**
     * 经纬度
     */
    private LocationPoint longitudeAndLatitude;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 更多联系人
     */
    private List<MoreContactsModel> moreContacts;
    /**
     * 关键词
     */
    private String keywords;

    public void setKeywords(String keywords) {
        if (!ObjectUtils.isEmpty(keywords) && keywords.length() > KEYWORDS_MAX_LENGTH) {
            keywords = keywords.substring(0, KEYWORDS_MAX_LENGTH);
        }
        this.keywords = keywords;
    }
    /**
     * 是否已删除
     */
    private boolean isDelete;

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete != null && isDelete == 1;
    }

    public void setIsDelete(boolean isDelete) {
        this.isDelete = isDelete;
    }
}
