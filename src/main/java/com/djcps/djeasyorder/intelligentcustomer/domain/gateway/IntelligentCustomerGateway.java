package com.djcps.djeasyorder.intelligentcustomer.domain.gateway;

import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.IntelligentCustomerMetadata;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;

import java.time.LocalDate;
import java.util.Iterator;

/**
 * 智能获客的数据门户
 */
public interface IntelligentCustomerGateway {

    /**
     * 保存智能获客获取的客户
     */
    void insert(IntelligentCustomer intelligentCustomer);

    /**
     * 通过元数据的id，删除智能获客获取的客户
     *
     * @param metadataId 元数据的id
     */
    void delete(String metadataId);

    /**
     * 通过id查询智能获客对象
     *
     * @param id 智能获客的客户的id
     * @return 查询对象
     */
    IntelligentCustomer findById(String id);

    /**
     * 通过线索的id查询智能获客对象
     *
     * @param clueId 关联关系id
     * @return 查询对象
     */
    IntelligentCustomer findByClueId(String clueId);

    /**
     * 通过表信息和日期参数，获取智能获客的元数据的迭代器
     *
     * @param scheme 表信息
     * @param date   日期
     * @param <T>    元数据类型
     * @return 元数据的迭代器
     */
    <T extends IntelligentCustomerMetadata> Iterator<T> iterator(Scheme<T> scheme, LocalDate date);
}
