package com.djcps.djeasyorder.intelligentcustomer.domain.metadata;

import com.alibaba.fastjson.JSONObject;
import com.djcps.djeasyorder.common.utils.RegularUtil;
import com.djcps.djeasyorder.customer.contants.CustomerConstant;
import com.djcps.djeasyorder.customer.model.po.expand.LocationPoint;
import com.djcps.djeasyorder.customer.model.bo.customer.MoreContactsModel;
import com.djcps.djeasyorder.feign.amap.model.GetAddressByLocationResult;
import com.djcps.djeasyorder.feign.amap.AmapApiUtil;
import com.djcps.djeasyorder.intelligentcustomer.domain.DataChannel;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 百度地图数据模型
 *
 * <AUTHOR>
 * @create 2021-06-02 10:50
 */
@Slf4j
@EqualsAndHashCode
@Data
@Configuration
public class BaiduMetadata implements IntelligentCustomerMetadata, ApplicationContextAware {

    /**
     * id
     */
    private String _id;
    /**
     * 名字
     */
    private String name;
    /**
     * 经纬度
     */
    private Location location;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 省名称
     */
    private String province;
    /**
     * 市名称
     */
    private String city;
    /**
     * 区名称
     */
    private String area;
    /**
     * 联系方式
     */
    private String telephone;
    /**
     * 链接地址
     */
    private String uri;
    /**
     * 关键词
     */
    private String match_key;
    /**
     * mongo数据创建时间
     */
    private Long create_time;
    /**
     * mongo数据修改时间
     */
    private Long update_time;
    /**
     * 是否删除 0或者null 未删除， 1已删除
     */
    private Integer is_delete;
    /**
     * 表名
     */
    private String tableName;

    private static AmapApiUtil amapApiUtil;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        amapApiUtil = applicationContext.getBean(AmapApiUtil.class);
    }

    /**
     * 百度地图手机号分隔符
     */
    private static final String PHONE_SPLIT = ",";

    @Override
    public IntelligentCustomer convert() {
        final IntelligentCustomer customer = new IntelligentCustomer();
        // 设置元数据信息
        customer.setChannel(DataChannel.BAIDU);
        customer.setMetadataId(get_id());
        customer.setMetadataTableName(tableName);
        customer.setMetadataCreateTime(getCreate_time());
        customer.setMetadataUpdateTime(getUpdate_time() == null ? getCreate_time() : getUpdate_time());
        customer.setIsDelete(getIs_delete());

        customer.setCustomerName(getName());
        customer.setUri(getUri());
        customer.setKeywords(getMatch_key());
        // 设置地址信息
        if (getLocation() != null) {
            try {
                final LocationPoint locationPoint = new LocationPoint(location.getLng(), location.getLat());
                customer.setLongitudeAndLatitude(locationPoint);
                GetAddressByLocationResult addressByLocation = amapApiUtil.getAddressByLocation(locationPoint.getLongitude(), locationPoint.getLatitude());
                if (addressByLocation == null) {
                    log.error("逆经纬度编码API调用失败");
                } else {
                    customer.setCustomerAddress(addressByLocation.getCustomerArea());
                    customer.setCityCode(addressByLocation.getCityCode());
                    customer.setStreetCode(addressByLocation.getStreetCode());
                }
            } catch (Exception e) {
                log.error("无法获取经纬度信息:{},Result:{}", JSONObject.toJSONString(location), JSONObject.toJSONString(location));
            }
        }
        if (ObjectUtils.isEmpty(customer.getCustomerAddress())) {
            customer.setCustomerAddress(province + city + area);
        }
        customer.setDetailAddress(getDetailAddress());

        // 设置联系方式信息
        if (!ObjectUtils.isEmpty(getTelephone())) {
            // 手机号码
            String contactWay = "";
            // 更多号码list
            List<MoreContactsModel> moreContacts = new ArrayList<>();
            for (String phone : getTelephone().split(PHONE_SPLIT)) {
                MoreContactsModel moreContact = null;
                if (RegularUtil.isPhone(phone)) {
                    if (ObjectUtils.isEmpty(contactWay)) {
                        contactWay = phone;
                    } else {
                        moreContact = new MoreContactsModel(null, morePhoneCut(phone));
                    }
                } else if (phone.startsWith("(")) {
                    // 如果是以 '(' 开头， 则是固号
                    int index = phone.indexOf(')');
                    // 获取固定号码前缀
                    String fixedPrefix = phone.substring(1, index);
                    // 获取固定号码后缀
                    String fixedSuffix = phone.substring(index + 1);
                    String morePhone = fixedPrefix + "-" + fixedSuffix;
                    boolean flag = RegularUtil.isTelOrPhone(morePhone);
                    if (!flag) {
                        log.info("固定号码格式错误:{}", phone);
                        continue;
                    }
                    moreContact = new MoreContactsModel(null, morePhoneCut(morePhone));
                } else if (RegularUtil.isTelOrPhone(phone)) {
                    moreContact = new MoreContactsModel(null, morePhoneCut(phone));
                } else {
                    log.info("手机号码格式错误:{}", phone);
                }
                if (moreContact != null && moreContacts.size() < CustomerConstant.MORE_CONTACT_MAX_SIZE) {
                    moreContacts.add(moreContact);
                }
            }
            // 设置更多号码
            customer.setMoreContacts(moreContacts);
            // 设置联系方式
            customer.setContactWay(contactWay);
        }
        return customer;
    }

    /**
     * 更多手机号截取
     */
    String morePhoneCut(String morePhone) {
        if (ObjectUtils.isEmpty(morePhone)) {
            return "";
        }
        int morePhoneMaxLength = 50;
        if (morePhone.length() > morePhoneMaxLength) {
            morePhone = morePhone.substring(0, morePhoneMaxLength);
        }
        return morePhone;
    }

    /**
     * 获取详细地址
     */
    public String getDetailAddress() {
        String detailAddress = address;
        // 如果address中包含省市区，则先替换掉（因为百度地图数据中，有些数据address包含省市区，有些不包含）
        if (!ObjectUtils.isEmpty(province)) {
            detailAddress = detailAddress.replaceAll(province, "");
        }
        if (!ObjectUtils.isEmpty(city)) {
            detailAddress = detailAddress.replaceAll(city, "");
        }
        if (!ObjectUtils.isEmpty(area)) {
            detailAddress = detailAddress.replaceAll(area, "");
        }
        return detailAddress;
    }

    /**
     * <AUTHOR>
     * @create 2021-06-04 10:02
     */
    @Data
    public static class Location {
        /**
         * 经度
         */
        private double lng;
        /**
         * 纬度
         */
        private double lat;
    }
}
