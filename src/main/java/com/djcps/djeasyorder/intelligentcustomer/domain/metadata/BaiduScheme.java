package com.djcps.djeasyorder.intelligentcustomer.domain.metadata;

import lombok.Getter;

/**
 * 百度地图表枚举
 *
 * <AUTHOR>
 * @create 2021-05-11 11:03
 */
@Getter
public enum BaiduScheme implements Scheme<BaiduMetadata> {

    BAIDU_POI_ANHUI("baidu_poi_anhui"),
    BAIDU_POI_BEIJING("baidu_poi_beijing"),
    BAIDU_POI_CHONGQING("baidu_poi_chongqing"),
    BAIDU_POI_FUJIAN("baidu_poi_fujian"),
    BAIDU_POI_GANSU("baidu_poi_gansu"),
    BAIDU_POI_GUANGDONG("baidu_poi_guangdong"),
    BAIDU_POI_GUANGXI("baidu_poi_guangxi"),
    BAIDU_POI_GUIZHOU("baidu_poi_guizhou"),
    BAIDU_POI_HAINAN("baidu_poi_hainan"),
    BAIDU_POI_HEBEI("baidu_poi_hebei"),
    BAIDU_POI_HEILONGJIANG("baidu_poi_heilongjiang"),
    BAIDU_POI_HENAN("baidu_poi_henan"),
    BAIDU_POI_HUBEI("baidu_poi_hubei"),
    BAIDU_POI_HUNAN("baidu_poi_hunan"),
    BAIDU_POI_JIANGSU("baidu_poi_jiangsu"),
    BAIDU_POI_JIANGXI("baidu_poi_jiangxi"),
    BAIDU_POI_JILIN("baidu_poi_jilin"),
    BAIDU_POI_LIAONING("baidu_poi_liaoning"),
    BAIDU_POI_NEIMENGGU("baidu_poi_neimenggu"),
    BAIDU_POI_NINGXIA("baidu_poi_ningxia"),
    BAIDU_POI_QINGHAI("baidu_poi_qinghai"),
    BAIDU_POI_SHAANXI("baidu_poi_shaanxi"),
    BAIDU_POI_SHANDONG("baidu_poi_shandong"),
    BAIDU_POI_SHANGHAI("baidu_poi_shanghai"),
    BAIDU_POI_SHANXI("baidu_poi_shanxi"),
    BAIDU_POI_SICHUAN("baidu_poi_sichuan"),
    BAIDU_POI_TIANJIN("baidu_poi_tianjin"),
    BAIDU_POI_XINJIANG("baidu_poi_xinjiang"),
    BAIDU_POI_XIZANG("baidu_poi_xizang"),
    BAIDU_POI_YUNNAN("baidu_poi_yunnan"),
    BAIDU_POI_ZHEJIANG("baidu_poi_zhejiang"),
    ;

    private final String tableName;
    private final Class<BaiduMetadata> modelClass;

    BaiduScheme(String tableName) {
        this.tableName = tableName;
        this.modelClass = BaiduMetadata.class;
    }
}
