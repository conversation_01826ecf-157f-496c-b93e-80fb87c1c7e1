package com.djcps.djeasyorder.intelligentcustomer.app;

import com.djcps.djeasyorder.common.constant.OrgConstant;
import com.djcps.djeasyorder.common.constant.SysParamEnum;
import com.djcps.djeasyorder.common.utils.UUIDUtil;
import com.djcps.djeasyorder.intelligentcustomer.client.api.IntelligentCustomerService;
import com.djcps.djeasyorder.intelligentcustomer.domain.DataChannel;
import com.djcps.djeasyorder.intelligentcustomer.domain.IntelligentCustomer;
import com.djcps.djeasyorder.intelligentcustomer.domain.gateway.IntelligentCustomerGateway;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.IntelligentCustomerMetadata;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dao.IntelligentCustomerDao;
import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerPo;
import com.djcps.djeasyorder.supers.service.impl.SysParamServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class IntelligentCustomerServiceImpl implements IntelligentCustomerService {

    @Autowired
    private IntelligentCustomerDao intelligentCustomerDao;
    @Autowired
    private IntelligentCustomerGateway gateway;
    @Autowired
    private SysParamServiceImpl sysParamService;

    @Override
    public void convertAndSave(IntelligentCustomerMetadata metadata) {
        final IntelligentCustomer intelligentCustomer = metadata.convert();
        if (intelligentCustomer.isDelete()) {
            gateway.delete(intelligentCustomer.getMetadataId());
        } else if (filter(intelligentCustomer)) {
            intelligentCustomer.setId(UUIDUtil.getUUID());
            intelligentCustomer.setCompanyId(OrgConstant.COMPANY_ID);
            gateway.insert(intelligentCustomer);
        }
    }

    @Override
    public void fetch(DataChannel dataChannel, LocalDate localDate) {
        for (Scheme<? extends IntelligentCustomerMetadata> scheme : dataChannel.getModelClass()) {
            fetch(scheme, localDate);
        }
    }

    @Override
    public void fetch(Scheme<? extends IntelligentCustomerMetadata> scheme, LocalDate localDate) {
        gateway.iterator(scheme, localDate).forEachRemaining(this::convertAndSave);
    }

    /**
     * 对智能获客的客户进行筛选，判断数据是否有效
     *
     * @param intelligentCustomer 智能获客的客户
     * @return 数据是否有效
     */
    public boolean filter(IntelligentCustomer intelligentCustomer) {
        // 过滤关键字
        if (containsIgnoredKeyword(intelligentCustomer.getCustomerName())) {
            return false;
        }
        // 手机号 或者 更多号码 只要一个不为空即可
        if (ObjectUtils.isEmpty(intelligentCustomer.getContactWay()) && ObjectUtils.isEmpty(intelligentCustomer.getMoreContacts())) {
            return false;
        }
        // 手机号去重
        if (!distinctPhone(intelligentCustomer.getContactWay())) {
            return false;
        }
        // 客户名字去重
        if (!distinctCustomerName(intelligentCustomer.getCustomerName())) {
            return false;
        }
        return intelligentCustomer.getCityCode() != null;
    }

    @Override
    public List<String> getIgnoredKeyword() {
        String value = sysParamService.getParamConfig(SysParamEnum.INTELLIGENT_CUSTOMER_IGNORED_KEYWORD);
        if (ObjectUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        return Arrays.asList(value.split(","));
    }

    @Override
    public boolean containsIgnoredKeyword(String str) {
        List<String> ignoredKeywordList = getIgnoredKeyword();
        // 关键字为空或者字符串为空 返回false
        if (ObjectUtils.isEmpty(ignoredKeywordList) || ObjectUtils.isEmpty(str)) {
            return false;
        }
        for (String keyword : ignoredKeywordList) {
            if (str.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean distinctPhone(String contactWay) {
        if (ObjectUtils.isEmpty(contactWay)) {
            return true;
        }
        CrmIntelligentCustomerPo customerPo = intelligentCustomerDao.getByPhone(contactWay);
        return ObjectUtils.isEmpty(customerPo);
    }

    @Override
    public boolean distinctCustomerName(String name) {
        if (ObjectUtils.isEmpty(name)) {
            return false;
        }
        List<CrmIntelligentCustomerPo> customerPoList = intelligentCustomerDao.getByCustomerName(name);
        return ObjectUtils.isEmpty(customerPoList);
    }
}
