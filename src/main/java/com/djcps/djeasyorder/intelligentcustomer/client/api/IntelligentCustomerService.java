package com.djcps.djeasyorder.intelligentcustomer.client.api;

import com.djcps.djeasyorder.intelligentcustomer.domain.DataChannel;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.IntelligentCustomerMetadata;
import com.djcps.djeasyorder.intelligentcustomer.domain.metadata.Scheme;

import java.time.LocalDate;
import java.util.List;

/**
 * 智能获客的服务
 */
public interface IntelligentCustomerService {

    /**
     * 对智能获客元数据进行持久化处理
     *
     * @param metadata 元数据
     */
    void convertAndSave(IntelligentCustomerMetadata metadata);

    /**
     * 从数据渠道获取智能获客数据
     *
     * @param dataChannel 数据渠道
     * @param localDate   日期条件
     */
    void fetch(DataChannel dataChannel, LocalDate localDate);

    /**
     * 从指定表获取智能获客数据
     *
     * @param scheme    数据表
     * @param localDate 日期条件
     */
    void fetch(Scheme<? extends IntelligentCustomerMetadata> scheme, LocalDate localDate);

    /**
     * 获取智能获客过滤关键字
     *
     * @return 关键字list
     * <AUTHOR>
     * @date 2021/6/4 10:21
     */
    List<String> getIgnoredKeyword();

    /**
     * 判断字符串中是否包含过滤关键字
     *
     * @param str 字符串
     * @return true:包含，false:不包含
     * <AUTHOR>
     * @date 2021/6/4 10:30
     */
    boolean containsIgnoredKeyword(String str);

    /**
     * 客户名称是否重复
     *
     * @param name 客户名称
     * @return true-数据不重复，false-数据重复
     * <AUTHOR>
     * @date 2021/7/21 11:08
     */
    boolean distinctCustomerName(String name);

    /**
     * 手机号是否重复
     *
     * @param contactWay 联系方式
     * @return 是否重复: true-数据不重复，false-数据重复
     * <AUTHOR>
     * @date 2021/6/7 9:20
     */
    boolean distinctPhone(String contactWay);
}
