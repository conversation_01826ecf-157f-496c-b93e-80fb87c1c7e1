package com.djcps.djeasyorder.intelligentcustomer.client.api;

import com.djcps.djeasyorder.intelligentcustomer.infrastructure.database.dataobject.CrmIntelligentCustomerPo;

/**
 * <AUTHOR>
 * @create 2021-05-11 9:45
 */
public interface CustomerRecommendService {

    /**
     * 智能推荐定时任务（每天0点执行）
     *
     * @param date 时间
     * <AUTHOR>
     * @date 2021/5/12 15:24
     */
    void intelligentRecommendTask(String date);

    /**
     * 给供应商推荐指定客户（测试用）
     *
     * @param supplierId            供应商id
     * @param intelligentCustomerId 智能获客表id
     * <AUTHOR>
     * @date 2021/5/18 15:16
     */
    void intelligentRecommendToSupplierTest(String supplierId, String intelligentCustomerId);

    /**
     * 给供应商推荐客户
     *
     * @param supplierId 供应商id
     * @param customerPo 推荐信息
     * <AUTHOR>
     * @date 2021/5/21 14:50
     */
    void intelligentRecommendToSupplier(String supplierId, CrmIntelligentCustomerPo customerPo);

    /**
     * 根据关键字删除智能获客数据
     *
     * <AUTHOR>
     * @date 2021/6/8 9:03
     */
    void delIntelligentCustomerForKeyword();

}