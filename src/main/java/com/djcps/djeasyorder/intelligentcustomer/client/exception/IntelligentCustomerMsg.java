package com.djcps.djeasyorder.intelligentcustomer.client.exception;

import com.djcps.djeasyorder.common.constant.SystemCodeEnum;
import com.djcps.framework.httpformat.message.MsgInterface;

/**
 * 智能获客提示语
 *
 * <AUTHOR>
 * @create 2021-06-02 15:01
 */
public enum IntelligentCustomerMsg implements MsgInterface {

    /**
     * msg
     */
    GET_CLEAN_ERROR("001", "无法获取清洗规则"),
    ;
    /**
     * 代码code
     */
    private String code;
    /**
     * 信息内容
     */
    private String msg;

    IntelligentCustomerMsg(String code, String msg) {
        this.code = SystemCodeEnum.SYS_CODE + SystemCodeEnum.INTELLIGENT_CUSTOMER + code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }
}
