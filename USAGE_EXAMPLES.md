# TaskResult API 使用示例

## 完整的业务流程示例

### 1. 保存任务结果

```bash
curl -X POST http://localhost:8080/DJEASYORDER/result/save.do \
  -H "Content-Type: application/json" \
  -d '{
    "skey": "400",
    "taskId": 123,
    "dataDate": "2025-07-31",
    "bizType": "deliver",
    "cycleType": "day",
    "execStartTime": "2025-08-05T10:00:00",
    "batchNo": "batch_001",
    "execEndTime": "2025-08-05T11:00:00",
    "userId": "user123",
    "orgId": "orgId_a7f2d136c681",
    "supplierId": "400",
    "role": "salesman"
  }'
```

**响应：**
```json
{
    "code": "10001200",
    "data": 123,
    "msg": "请求成功！",
    "success": true
}
```

### 2. 保存任务结果子项

使用上一步返回的ID（123）作为resultId：

```bash
curl -X POST http://localhost:8080/DJEASYORDER/result/saveItem.do \
  -H "Content-Type: application/json" \
  -d '{
    "resultId": 123,
    "taskId": 123,
    "templateId": 456,
    "originData": "原始数据内容",
    "skey": "400",
    "prompt": "请分析以下数据",
    "subBizType": "summary",
    "batchNo": "batch_001"
  }'
```

**响应：**
```json
{
    "code": "10001200",
    "data": 789,
    "msg": "请求成功！",
    "success": true
}
```

### 3. 更新任务结果子项

使用上一步返回的ID（789）作为taskItemId：

```bash
curl -X POST http://localhost:8080/DJEASYORDER/result/updateItem.do \
  -H "Content-Type: application/json" \
  -d '{
    "taskItemId": 789,
    "analysisResult": "分析完成：数据显示销售额增长15%"
  }'
```

**响应：**
```json
{
    "code": "10001200",
    "data": "更新成功",
    "msg": "请求成功！",
    "success": true
}
```

### 4. 查询任务结果

```bash
curl -X POST http://localhost:8080/DJEASYORDER/result/page.do \
  -H "Content-Type: application/json" \
  -d '{
    "skey": "400",
    "supplierId": "400",
    "orgId": "orgId_a7f2d136c681",
    "role": "salesman",
    "cycleType": "day",
    "bizType": "deliver",
    "pageNo": 1,
    "pageSize": 10
  }'
```

### 5. 删除任务结果

```bash
curl -X POST http://localhost:8080/DJEASYORDER/result/delete.do \
  -H "Content-Type: application/json" \
  -d '{
    "skey": "400",
    "taskId": 123,
    "dataDate": "2025-07-31",
    "cycleType": "day",
    "supplierId": "400"
  }'
```

**响应：**
```json
{
    "code": "10001200",
    "data": "删除成功",
    "msg": "请求成功！",
    "success": true
}
```

## 错误处理示例

### 参数验证失败

```json
{
    "code": "10001400",
    "data": null,
    "msg": "参数验证失败：skey不能为空",
    "success": false
}
```

### 数据不存在

```json
{
    "code": "10001404",
    "data": null,
    "msg": "数据不存在",
    "success": false
}
```
